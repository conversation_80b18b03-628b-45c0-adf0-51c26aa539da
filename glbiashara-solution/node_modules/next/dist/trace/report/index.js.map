{"version": 3, "sources": ["../../../src/trace/report/index.ts"], "sourcesContent": ["import type { TraceEvent } from '../types'\nimport reportToTelemetry from './to-telemetry'\nimport reportToJson from './to-json'\nimport type { Reporter } from './types'\n\nclass MultiReporter implements Reporter {\n  private reporters: Reporter[] = []\n\n  constructor(reporters: Reporter[]) {\n    this.reporters = reporters\n  }\n\n  async flushAll(opts?: { end: boolean }) {\n    await Promise.all(this.reporters.map((reporter) => reporter.flushAll(opts)))\n  }\n\n  report(event: TraceEvent) {\n    this.reporters.forEach((reporter) => reporter.report(event))\n  }\n}\n\n// JSON is always reported to allow for diagnostics\nexport const reporter = new MultiReporter([reportToJson, reportToTelemetry])\n"], "names": ["reporter", "MultiReporter", "constructor", "reporters", "flushAll", "opts", "Promise", "all", "map", "report", "event", "for<PERSON>ach", "reportToJson", "reportToTelemetry"], "mappings": ";;;;+BAsBaA;;;eAAAA;;;oEArBiB;+DACL;;;;;;AAGzB,MAAMC;IAGJC,YAAYC,SAAqB,CAAE;aAF3BA,YAAwB,EAAE;QAGhC,IAAI,CAACA,SAAS,GAAGA;IACnB;IAEA,MAAMC,SAASC,IAAuB,EAAE;QACtC,MAAMC,QAAQC,GAAG,CAAC,IAAI,CAACJ,SAAS,CAACK,GAAG,CAAC,CAACR,WAAaA,SAASI,QAAQ,CAACC;IACvE;IAEAI,OAAOC,KAAiB,EAAE;QACxB,IAAI,CAACP,SAAS,CAACQ,OAAO,CAAC,CAACX,WAAaA,SAASS,MAAM,CAACC;IACvD;AACF;AAGO,MAAMV,WAAW,IAAIC,cAAc;IAACW,eAAY;IAAEC,oBAAiB;CAAC", "ignoreList": [0]}