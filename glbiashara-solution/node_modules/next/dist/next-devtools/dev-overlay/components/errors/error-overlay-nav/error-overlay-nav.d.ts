import type { VersionInfo } from '../../../../../server/dev/parse-version-info';
import type { ReadyRuntimeError } from '../../../utils/get-error-by-type';
type ErrorOverlayNavProps = {
    runtimeErrors?: ReadyRuntimeError[];
    activeIdx?: number;
    setActiveIndex?: (index: number) => void;
    versionInfo?: VersionInfo;
    isTurbopack?: boolean;
};
export declare function ErrorOverlayNav({ runtimeErrors, activeIdx, setActiveIndex, versionInfo, }: ErrorOverlayNavProps): import("react/jsx-runtime").JSX.Element;
export declare const styles = "\n  [data-nextjs-error-overlay-nav] {\n    --stroke-color: var(--color-gray-400);\n    --background-color: var(--color-background-100);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    width: 100%;\n\n    position: relative;\n    z-index: 2;\n    outline: none;\n    translate: var(--next-dialog-border-width) var(--next-dialog-border-width);\n    max-width: var(--next-dialog-max-width);\n\n    .error-overlay-notch {\n      translate: calc(var(--next-dialog-border-width) * -1);\n      width: auto;\n      height: var(--next-dialog-notch-height);\n      padding: 12px;\n      background: var(--background-color);\n      border: var(--next-dialog-border-width) solid var(--stroke-color);\n      border-bottom: none;\n      position: relative;\n\n      &[data-side='left'] {\n        padding-right: 0;\n        border-radius: var(--next-dialog-radius) 0 0 0;\n\n        .error-overlay-notch-tail {\n          right: -54px;\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-right: -10px;\n        }\n      }\n\n      &[data-side='right'] {\n        padding-left: 0;\n        border-radius: 0 var(--next-dialog-radius) 0 0;\n\n        .error-overlay-notch-tail {\n          left: -54px;\n          transform: rotateY(180deg);\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-left: -12px;\n        }\n      }\n\n      .error-overlay-notch-tail {\n        position: absolute;\n        top: calc(var(--next-dialog-border-width) * -1);\n        pointer-events: none;\n        z-index: -1;\n        height: calc(100% + var(--next-dialog-border-width));\n      }\n    }\n  }\n\n  @media (max-width: 600px) {\n    [data-nextjs-error-overlay-nav] {\n      background: var(--background-color);\n      border-radius: var(--next-dialog-radius) var(--next-dialog-radius) 0 0;\n      border: var(--next-dialog-border-width) solid var(--stroke-color);\n      border-bottom: none;\n      overflow: hidden;\n      translate: 0 var(--next-dialog-border-width);\n      \n      .error-overlay-notch {\n        border-radius: 0;\n        border: 0;\n\n        &[data-side=\"left\"], &[data-side=\"right\"] {\n          border-radius: 0;\n        }\n\n        .error-overlay-notch-tail {\n          display: none;\n        }\n      }\n    }\n  }\n";
export {};
