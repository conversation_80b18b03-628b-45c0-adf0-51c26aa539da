(()=>{var __webpack_modules__={"./node_modules/.pnpm/@ampproject+remapping@2.3.0/node_modules/@ampproject/remapping/dist/remapping.umd.js":function(module,__unused_webpack_exports,__webpack_require__){module.exports=function(traceMapping,genMapping){"use strict";const SOURCELESS_MAPPING=SegmentObject("",-1,-1,"",null,!1),EMPTY_SOURCES=[];function SegmentObject(source,line,column,name,content,ignore){return{source,line,column,name,content,ignore}}function Source(map,sources,source,content,ignore){return{map,sources,source,content,ignore}}function MapSource(map,sources){return Source(map,sources,"",null,!1)}function OriginalSource(source,content,ignore){return Source(null,EMPTY_SOURCES,source,content,ignore)}function traceMappings(tree){const gen=new genMapping.GenMapping({file:tree.map.file}),{sources:rootSources,map}=tree,rootNames=map.names,rootMappings=traceMapping.decodedMappings(map);for(let i=0;i<rootMappings.length;i++){const segments=rootMappings[i];for(let j=0;j<segments.length;j++){const segment=segments[j],genCol=segment[0];let traced=SOURCELESS_MAPPING;if(1!==segment.length&&(traced=originalPositionFor(rootSources[segment[1]],segment[2],segment[3],5===segment.length?rootNames[segment[4]]:""),null==traced))continue;const{column,line,name,content,source,ignore}=traced;genMapping.maybeAddSegment(gen,i,genCol,source,line,column,name),source&&null!=content&&genMapping.setSourceContent(gen,source,content),ignore&&genMapping.setIgnore(gen,source,!0)}}return gen}function originalPositionFor(source,line,column,name){if(!source.map)return SegmentObject(source.source,line,column,name,source.content,source.ignore);const segment=traceMapping.traceSegment(source.map,line,column);return null==segment?null:1===segment.length?SOURCELESS_MAPPING:originalPositionFor(source.sources[segment[1]],segment[2],segment[3],5===segment.length?source.map.names[segment[4]]:name)}function asArray(value){return Array.isArray(value)?value:[value]}function buildSourceMapTree(input,loader){const maps=asArray(input).map((m=>new traceMapping.TraceMap(m,""))),map=maps.pop();for(let i=0;i<maps.length;i++)if(maps[i].sources.length>1)throw new Error(`Transformation map ${i} must have exactly one source file.\nDid you specify these with the most recent transformation maps first?`);let tree=build(map,loader,"",0);for(let i=maps.length-1;i>=0;i--)tree=MapSource(maps[i],[tree]);return tree}function build(map,loader,importer,importerDepth){const{resolvedSources,sourcesContent,ignoreList}=map,depth=importerDepth+1;return MapSource(map,resolvedSources.map(((sourceFile,i)=>{const ctx={importer,depth,source:sourceFile||"",content:void 0,ignore:void 0},sourceMap=loader(ctx.source,ctx),{source,content,ignore}=ctx;return sourceMap?build(new traceMapping.TraceMap(sourceMap,source),loader,source,depth):OriginalSource(source,void 0!==content?content:sourcesContent?sourcesContent[i]:null,void 0!==ignore?ignore:!!ignoreList&&ignoreList.includes(i))})))}class SourceMap{constructor(map,options){const out=options.decodedMappings?genMapping.toDecodedMap(map):genMapping.toEncodedMap(map);this.version=out.version,this.file=out.file,this.mappings=out.mappings,this.names=out.names,this.ignoreList=out.ignoreList,this.sourceRoot=out.sourceRoot,this.sources=out.sources,options.excludeContent||(this.sourcesContent=out.sourcesContent)}toString(){return JSON.stringify(this)}}function remapping(input,loader,options){const opts="object"==typeof options?options:{excludeContent:!!options,decodedMappings:!1},tree=buildSourceMapTree(input,loader);return new SourceMap(traceMappings(tree),opts)}return remapping}(__webpack_require__("./node_modules/.pnpm/@jridgewell+trace-mapping@0.3.25/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js"),__webpack_require__("./node_modules/.pnpm/@jridgewell+gen-mapping@0.3.8/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js"))},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files lazy recursive":module=>{function webpackEmptyAsyncContext(req){return Promise.resolve().then((()=>{var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}))}webpackEmptyAsyncContext.keys=()=>[],webpackEmptyAsyncContext.resolve=webpackEmptyAsyncContext,webpackEmptyAsyncContext.id="./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files lazy recursive",module.exports=webpackEmptyAsyncContext},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive",module.exports=webpackEmptyContext},"./node_modules/.pnpm/@babel+plugin-syntax-class-properties@7.12.13_@babel+core@7.26.0/node_modules/@babel/plugin-syntax-class-properties/lib/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";exports.A=void 0;var _default=(0,__webpack_require__("./node_modules/.pnpm/@babel+helper-plugin-utils@7.25.9/node_modules/@babel/helper-plugin-utils/lib/index.js").declare)((api=>(api.assertVersion(7),{name:"syntax-class-properties",manipulateOptions(opts,parserOpts){parserOpts.plugins.push("classProperties","classPrivateProperties","classPrivateMethods")}})));exports.A=_default},"./node_modules/.pnpm/@jridgewell+gen-mapping@0.3.8/node_modules/@jridgewell/gen-mapping/dist/gen-mapping.umd.js":function(__unused_webpack_module,exports,__webpack_require__){!function(exports,setArray,sourcemapCodec,traceMapping){"use strict";const COLUMN=0,SOURCES_INDEX=1,SOURCE_LINE=2,SOURCE_COLUMN=3,NAMES_INDEX=4,NO_NAME=-1;class GenMapping{constructor({file,sourceRoot}={}){this._names=new setArray.SetArray,this._sources=new setArray.SetArray,this._sourcesContent=[],this._mappings=[],this.file=file,this.sourceRoot=sourceRoot,this._ignoreList=new setArray.SetArray}}function cast(map){return map}function addSegment(map,genLine,genColumn,source,sourceLine,sourceColumn,name,content){return addSegmentInternal(!1,map,genLine,genColumn,source,sourceLine,sourceColumn,name,content)}function addMapping(map,mapping){return addMappingInternal(!1,map,mapping)}const maybeAddSegment=(map,genLine,genColumn,source,sourceLine,sourceColumn,name,content)=>addSegmentInternal(!0,map,genLine,genColumn,source,sourceLine,sourceColumn,name,content),maybeAddMapping=(map,mapping)=>addMappingInternal(!0,map,mapping);function setSourceContent(map,source,content){const{_sources:sources,_sourcesContent:sourcesContent}=cast(map);sourcesContent[setArray.put(sources,source)]=content}function setIgnore(map,source,ignore=!0){const{_sources:sources,_sourcesContent:sourcesContent,_ignoreList:ignoreList}=cast(map),index=setArray.put(sources,source);index===sourcesContent.length&&(sourcesContent[index]=null),ignore?setArray.put(ignoreList,index):setArray.remove(ignoreList,index)}function toDecodedMap(map){const{_mappings:mappings,_sources:sources,_sourcesContent:sourcesContent,_names:names,_ignoreList:ignoreList}=cast(map);return removeEmptyFinalLines(mappings),{version:3,file:map.file||void 0,names:names.array,sourceRoot:map.sourceRoot||void 0,sources:sources.array,sourcesContent,mappings,ignoreList:ignoreList.array}}function toEncodedMap(map){const decoded=toDecodedMap(map);return Object.assign(Object.assign({},decoded),{mappings:sourcemapCodec.encode(decoded.mappings)})}function fromMap(input){const map=new traceMapping.TraceMap(input),gen=new GenMapping({file:map.file,sourceRoot:map.sourceRoot});return putAll(cast(gen)._names,map.names),putAll(cast(gen)._sources,map.sources),cast(gen)._sourcesContent=map.sourcesContent||map.sources.map((()=>null)),cast(gen)._mappings=traceMapping.decodedMappings(map),map.ignoreList&&putAll(cast(gen)._ignoreList,map.ignoreList),gen}function allMappings(map){const out=[],{_mappings:mappings,_sources:sources,_names:names}=cast(map);for(let i=0;i<mappings.length;i++){const line=mappings[i];for(let j=0;j<line.length;j++){const seg=line[j],generated={line:i+1,column:seg[COLUMN]};let source,original,name;1!==seg.length&&(source=sources.array[seg[SOURCES_INDEX]],original={line:seg[SOURCE_LINE]+1,column:seg[SOURCE_COLUMN]},5===seg.length&&(name=names.array[seg[NAMES_INDEX]])),out.push({generated,source,original,name})}}return out}function addSegmentInternal(skipable,map,genLine,genColumn,source,sourceLine,sourceColumn,name,content){const{_mappings:mappings,_sources:sources,_sourcesContent:sourcesContent,_names:names}=cast(map),line=getLine(mappings,genLine),index=getColumnIndex(line,genColumn);if(!source){if(skipable&&skipSourceless(line,index))return;return insert(line,index,[genColumn])}const sourcesIndex=setArray.put(sources,source),namesIndex=name?setArray.put(names,name):NO_NAME;if(sourcesIndex===sourcesContent.length&&(sourcesContent[sourcesIndex]=null!=content?content:null),!skipable||!skipSource(line,index,sourcesIndex,sourceLine,sourceColumn,namesIndex))return insert(line,index,name?[genColumn,sourcesIndex,sourceLine,sourceColumn,namesIndex]:[genColumn,sourcesIndex,sourceLine,sourceColumn])}function getLine(mappings,index){for(let i=mappings.length;i<=index;i++)mappings[i]=[];return mappings[index]}function getColumnIndex(line,genColumn){let index=line.length;for(let i=index-1;i>=0&&!(genColumn>=line[i][COLUMN]);index=i--);return index}function insert(array,index,value){for(let i=array.length;i>index;i--)array[i]=array[i-1];array[index]=value}function removeEmptyFinalLines(mappings){const{length}=mappings;let len=length;for(let i=len-1;i>=0&&!(mappings[i].length>0);len=i,i--);len<length&&(mappings.length=len)}function putAll(setarr,array){for(let i=0;i<array.length;i++)setArray.put(setarr,array[i])}function skipSourceless(line,index){return 0===index||1===line[index-1].length}function skipSource(line,index,sourcesIndex,sourceLine,sourceColumn,namesIndex){if(0===index)return!1;const prev=line[index-1];return 1!==prev.length&&sourcesIndex===prev[SOURCES_INDEX]&&sourceLine===prev[SOURCE_LINE]&&sourceColumn===prev[SOURCE_COLUMN]&&namesIndex===(5===prev.length?prev[NAMES_INDEX]:NO_NAME)}function addMappingInternal(skipable,map,mapping){const{generated,source,original,name,content}=mapping;return source?addSegmentInternal(skipable,map,generated.line-1,generated.column,source,original.line-1,original.column,name,content):addSegmentInternal(skipable,map,generated.line-1,generated.column,null,null,null,null,null)}exports.GenMapping=GenMapping,exports.addMapping=addMapping,exports.addSegment=addSegment,exports.allMappings=allMappings,exports.fromMap=fromMap,exports.maybeAddMapping=maybeAddMapping,exports.maybeAddSegment=maybeAddSegment,exports.setIgnore=setIgnore,exports.setSourceContent=setSourceContent,exports.toDecodedMap=toDecodedMap,exports.toEncodedMap=toEncodedMap,Object.defineProperty(exports,"__esModule",{value:!0})}(exports,__webpack_require__("./node_modules/.pnpm/@jridgewell+set-array@1.2.1/node_modules/@jridgewell/set-array/dist/set-array.umd.js"),__webpack_require__("./node_modules/.pnpm/@jridgewell+sourcemap-codec@1.5.0/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js"),__webpack_require__("./node_modules/.pnpm/@jridgewell+trace-mapping@0.3.25/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js"))},"./node_modules/.pnpm/@jridgewell+resolve-uri@3.1.2/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js":function(module){module.exports=function(){"use strict";const schemeRegex=/^[\w+.-]+:\/\//,urlRegex=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,fileRegex=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i;function isAbsoluteUrl(input){return schemeRegex.test(input)}function isSchemeRelativeUrl(input){return input.startsWith("//")}function isAbsolutePath(input){return input.startsWith("/")}function isFileUrl(input){return input.startsWith("file:")}function isRelative(input){return/^[.?#]/.test(input)}function parseAbsoluteUrl(input){const match=urlRegex.exec(input);return makeUrl(match[1],match[2]||"",match[3],match[4]||"",match[5]||"/",match[6]||"",match[7]||"")}function parseFileUrl(input){const match=fileRegex.exec(input),path=match[2];return makeUrl("file:","",match[1]||"","",isAbsolutePath(path)?path:"/"+path,match[3]||"",match[4]||"")}function makeUrl(scheme,user,host,port,path,query,hash){return{scheme,user,host,port,path,query,hash,type:7}}function parseUrl(input){if(isSchemeRelativeUrl(input)){const url=parseAbsoluteUrl("http:"+input);return url.scheme="",url.type=6,url}if(isAbsolutePath(input)){const url=parseAbsoluteUrl("http://foo.com"+input);return url.scheme="",url.host="",url.type=5,url}if(isFileUrl(input))return parseFileUrl(input);if(isAbsoluteUrl(input))return parseAbsoluteUrl(input);const url=parseAbsoluteUrl("http://foo.com/"+input);return url.scheme="",url.host="",url.type=input?input.startsWith("?")?3:input.startsWith("#")?2:4:1,url}function stripPathFilename(path){if(path.endsWith("/.."))return path;const index=path.lastIndexOf("/");return path.slice(0,index+1)}function mergePaths(url,base){normalizePath(base,base.type),"/"===url.path?url.path=base.path:url.path=stripPathFilename(base.path)+url.path}function normalizePath(url,type){const rel=type<=4,pieces=url.path.split("/");let pointer=1,positive=0,addTrailingSlash=!1;for(let i=1;i<pieces.length;i++){const piece=pieces[i];piece?(addTrailingSlash=!1,"."!==piece&&(".."!==piece?(pieces[pointer++]=piece,positive++):positive?(addTrailingSlash=!0,positive--,pointer--):rel&&(pieces[pointer++]=piece))):addTrailingSlash=!0}let path="";for(let i=1;i<pointer;i++)path+="/"+pieces[i];(!path||addTrailingSlash&&!path.endsWith("/.."))&&(path+="/"),url.path=path}function resolve(input,base){if(!input&&!base)return"";const url=parseUrl(input);let inputType=url.type;if(base&&7!==inputType){const baseUrl=parseUrl(base),baseType=baseUrl.type;switch(inputType){case 1:url.hash=baseUrl.hash;case 2:url.query=baseUrl.query;case 3:case 4:mergePaths(url,baseUrl);case 5:url.user=baseUrl.user,url.host=baseUrl.host,url.port=baseUrl.port;case 6:url.scheme=baseUrl.scheme}baseType>inputType&&(inputType=baseType)}normalizePath(url,inputType);const queryHash=url.query+url.hash;switch(inputType){case 2:case 3:return queryHash;case 4:{const path=url.path.slice(1);return path?isRelative(base||input)&&!isRelative(path)?"./"+path+queryHash:path+queryHash:queryHash||"."}case 5:return url.path+queryHash;default:return url.scheme+"//"+url.user+url.host+url.port+url.path+queryHash}}return resolve}()},"./node_modules/.pnpm/@jridgewell+set-array@1.2.1/node_modules/@jridgewell/set-array/dist/set-array.umd.js":function(__unused_webpack_module,exports){!function(exports){"use strict";class SetArray{constructor(){this._indexes={__proto__:null},this.array=[]}}function cast(set){return set}function get(setarr,key){return cast(setarr)._indexes[key]}function put(setarr,key){const index=get(setarr,key);if(void 0!==index)return index;const{array,_indexes:indexes}=cast(setarr),length=array.push(key);return indexes[key]=length-1}function pop(setarr){const{array,_indexes:indexes}=cast(setarr);0!==array.length&&(indexes[array.pop()]=void 0)}function remove(setarr,key){const index=get(setarr,key);if(void 0===index)return;const{array,_indexes:indexes}=cast(setarr);for(let i=index+1;i<array.length;i++){const k=array[i];array[i-1]=k,indexes[k]--}indexes[key]=void 0,array.pop()}exports.SetArray=SetArray,exports.get=get,exports.pop=pop,exports.put=put,exports.remove=remove,Object.defineProperty(exports,"__esModule",{value:!0})}(exports)},"./node_modules/.pnpm/@jridgewell+sourcemap-codec@1.5.0/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js":function(__unused_webpack_module,exports){!function(exports){"use strict";const comma=",".charCodeAt(0),semicolon=";".charCodeAt(0),chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",intToChar=new Uint8Array(64),charToInt=new Uint8Array(128);for(let i=0;i<chars.length;i++){const c=chars.charCodeAt(i);intToChar[i]=c,charToInt[c]=i}function decodeInteger(reader,relative){let value=0,shift=0,integer=0;do{const c=reader.next();integer=charToInt[c],value|=(31&integer)<<shift,shift+=5}while(32&integer);const shouldNegate=1&value;return value>>>=1,shouldNegate&&(value=-2147483648|-value),relative+value}function encodeInteger(builder,num,relative){let delta=num-relative;delta=delta<0?-delta<<1|1:delta<<1;do{let clamped=31&delta;delta>>>=5,delta>0&&(clamped|=32),builder.write(intToChar[clamped])}while(delta>0);return num}function hasMoreVlq(reader,max){return!(reader.pos>=max)&&reader.peek()!==comma}const bufLength=16384,td="undefined"!=typeof TextDecoder?new TextDecoder:"undefined"!=typeof Buffer?{decode:buf=>Buffer.from(buf.buffer,buf.byteOffset,buf.byteLength).toString()}:{decode(buf){let out="";for(let i=0;i<buf.length;i++)out+=String.fromCharCode(buf[i]);return out}};class StringWriter{constructor(){this.pos=0,this.out="",this.buffer=new Uint8Array(bufLength)}write(v){const{buffer}=this;buffer[this.pos++]=v,this.pos===bufLength&&(this.out+=td.decode(buffer),this.pos=0)}flush(){const{buffer,out,pos}=this;return pos>0?out+td.decode(buffer.subarray(0,pos)):out}}class StringReader{constructor(buffer){this.pos=0,this.buffer=buffer}next(){return this.buffer.charCodeAt(this.pos++)}peek(){return this.buffer.charCodeAt(this.pos)}indexOf(char){const{buffer,pos}=this,idx=buffer.indexOf(char,pos);return-1===idx?buffer.length:idx}}const EMPTY=[];function decodeOriginalScopes(input){const{length}=input,reader=new StringReader(input),scopes=[],stack=[];let line=0;for(;reader.pos<length;reader.pos++){line=decodeInteger(reader,line);const column=decodeInteger(reader,0);if(!hasMoreVlq(reader,length)){const last=stack.pop();last[2]=line,last[3]=column;continue}const kind=decodeInteger(reader,0),scope=1&decodeInteger(reader,0)?[line,column,0,0,kind,decodeInteger(reader,0)]:[line,column,0,0,kind];let vars=EMPTY;if(hasMoreVlq(reader,length)){vars=[];do{const varsIndex=decodeInteger(reader,0);vars.push(varsIndex)}while(hasMoreVlq(reader,length))}scope.vars=vars,scopes.push(scope),stack.push(scope)}return scopes}function encodeOriginalScopes(scopes){const writer=new StringWriter;for(let i=0;i<scopes.length;)i=_encodeOriginalScopes(scopes,i,writer,[0]);return writer.flush()}function _encodeOriginalScopes(scopes,index,writer,state){const scope=scopes[index],{0:startLine,1:startColumn,2:endLine,3:endColumn,4:kind,vars}=scope;index>0&&writer.write(comma),state[0]=encodeInteger(writer,startLine,state[0]),encodeInteger(writer,startColumn,0),encodeInteger(writer,kind,0),encodeInteger(writer,6===scope.length?1:0,0),6===scope.length&&encodeInteger(writer,scope[5],0);for(const v of vars)encodeInteger(writer,v,0);for(index++;index<scopes.length;){const next=scopes[index],{0:l,1:c}=next;if(l>endLine||l===endLine&&c>=endColumn)break;index=_encodeOriginalScopes(scopes,index,writer,state)}return writer.write(comma),state[0]=encodeInteger(writer,endLine,state[0]),encodeInteger(writer,endColumn,0),index}function decodeGeneratedRanges(input){const{length}=input,reader=new StringReader(input),ranges=[],stack=[];let genLine=0,definitionSourcesIndex=0,definitionScopeIndex=0,callsiteSourcesIndex=0,callsiteLine=0,callsiteColumn=0,bindingLine=0,bindingColumn=0;do{const semi=reader.indexOf(";");let genColumn=0;for(;reader.pos<semi;reader.pos++){if(genColumn=decodeInteger(reader,genColumn),!hasMoreVlq(reader,semi)){const last=stack.pop();last[2]=genLine,last[3]=genColumn;continue}const fields=decodeInteger(reader,0),hasCallsite=2&fields,hasScope=4&fields;let range,callsite=null,bindings=EMPTY;if(1&fields){const defSourcesIndex=decodeInteger(reader,definitionSourcesIndex);definitionScopeIndex=decodeInteger(reader,definitionSourcesIndex===defSourcesIndex?definitionScopeIndex:0),definitionSourcesIndex=defSourcesIndex,range=[genLine,genColumn,0,0,defSourcesIndex,definitionScopeIndex]}else range=[genLine,genColumn,0,0];if(range.isScope=!!hasScope,hasCallsite){const prevCsi=callsiteSourcesIndex,prevLine=callsiteLine;callsiteSourcesIndex=decodeInteger(reader,callsiteSourcesIndex);const sameSource=prevCsi===callsiteSourcesIndex;callsiteLine=decodeInteger(reader,sameSource?callsiteLine:0),callsiteColumn=decodeInteger(reader,sameSource&&prevLine===callsiteLine?callsiteColumn:0),callsite=[callsiteSourcesIndex,callsiteLine,callsiteColumn]}if(range.callsite=callsite,hasMoreVlq(reader,semi)){bindings=[];do{bindingLine=genLine,bindingColumn=genColumn;const expressionsCount=decodeInteger(reader,0);let expressionRanges;if(expressionsCount<-1){expressionRanges=[[decodeInteger(reader,0)]];for(let i=-1;i>expressionsCount;i--){const prevBl=bindingLine;bindingLine=decodeInteger(reader,bindingLine),bindingColumn=decodeInteger(reader,bindingLine===prevBl?bindingColumn:0);const expression=decodeInteger(reader,0);expressionRanges.push([expression,bindingLine,bindingColumn])}}else expressionRanges=[[expressionsCount]];bindings.push(expressionRanges)}while(hasMoreVlq(reader,semi))}range.bindings=bindings,ranges.push(range),stack.push(range)}genLine++,reader.pos=semi+1}while(reader.pos<length);return ranges}function encodeGeneratedRanges(ranges){if(0===ranges.length)return"";const writer=new StringWriter;for(let i=0;i<ranges.length;)i=_encodeGeneratedRanges(ranges,i,writer,[0,0,0,0,0,0,0]);return writer.flush()}function _encodeGeneratedRanges(ranges,index,writer,state){const range=ranges[index],{0:startLine,1:startColumn,2:endLine,3:endColumn,isScope,callsite,bindings}=range;if(state[0]<startLine?(catchupLine(writer,state[0],startLine),state[0]=startLine,state[1]=0):index>0&&writer.write(comma),state[1]=encodeInteger(writer,range[1],state[1]),encodeInteger(writer,(6===range.length?1:0)|(callsite?2:0)|(isScope?4:0),0),6===range.length){const{4:sourcesIndex,5:scopesIndex}=range;sourcesIndex!==state[2]&&(state[3]=0),state[2]=encodeInteger(writer,sourcesIndex,state[2]),state[3]=encodeInteger(writer,scopesIndex,state[3])}if(callsite){const{0:sourcesIndex,1:callLine,2:callColumn}=range.callsite;sourcesIndex!==state[4]?(state[5]=0,state[6]=0):callLine!==state[5]&&(state[6]=0),state[4]=encodeInteger(writer,sourcesIndex,state[4]),state[5]=encodeInteger(writer,callLine,state[5]),state[6]=encodeInteger(writer,callColumn,state[6])}if(bindings)for(const binding of bindings){binding.length>1&&encodeInteger(writer,-binding.length,0),encodeInteger(writer,binding[0][0],0);let bindingStartLine=startLine,bindingStartColumn=startColumn;for(let i=1;i<binding.length;i++){const expRange=binding[i];bindingStartLine=encodeInteger(writer,expRange[1],bindingStartLine),bindingStartColumn=encodeInteger(writer,expRange[2],bindingStartColumn),encodeInteger(writer,expRange[0],0)}}for(index++;index<ranges.length;){const next=ranges[index],{0:l,1:c}=next;if(l>endLine||l===endLine&&c>=endColumn)break;index=_encodeGeneratedRanges(ranges,index,writer,state)}return state[0]<endLine?(catchupLine(writer,state[0],endLine),state[0]=endLine,state[1]=0):writer.write(comma),state[1]=encodeInteger(writer,endColumn,state[1]),index}function catchupLine(writer,lastLine,line){do{writer.write(semicolon)}while(++lastLine<line)}function decode(mappings){const{length}=mappings,reader=new StringReader(mappings),decoded=[];let genColumn=0,sourcesIndex=0,sourceLine=0,sourceColumn=0,namesIndex=0;do{const semi=reader.indexOf(";"),line=[];let sorted=!0,lastCol=0;for(genColumn=0;reader.pos<semi;){let seg;genColumn=decodeInteger(reader,genColumn),genColumn<lastCol&&(sorted=!1),lastCol=genColumn,hasMoreVlq(reader,semi)?(sourcesIndex=decodeInteger(reader,sourcesIndex),sourceLine=decodeInteger(reader,sourceLine),sourceColumn=decodeInteger(reader,sourceColumn),hasMoreVlq(reader,semi)?(namesIndex=decodeInteger(reader,namesIndex),seg=[genColumn,sourcesIndex,sourceLine,sourceColumn,namesIndex]):seg=[genColumn,sourcesIndex,sourceLine,sourceColumn]):seg=[genColumn],line.push(seg),reader.pos++}sorted||sort(line),decoded.push(line),reader.pos=semi+1}while(reader.pos<=length);return decoded}function sort(line){line.sort(sortComparator)}function sortComparator(a,b){return a[0]-b[0]}function encode(decoded){const writer=new StringWriter;let sourcesIndex=0,sourceLine=0,sourceColumn=0,namesIndex=0;for(let i=0;i<decoded.length;i++){const line=decoded[i];if(i>0&&writer.write(semicolon),0===line.length)continue;let genColumn=0;for(let j=0;j<line.length;j++){const segment=line[j];j>0&&writer.write(comma),genColumn=encodeInteger(writer,segment[0],genColumn),1!==segment.length&&(sourcesIndex=encodeInteger(writer,segment[1],sourcesIndex),sourceLine=encodeInteger(writer,segment[2],sourceLine),sourceColumn=encodeInteger(writer,segment[3],sourceColumn),4!==segment.length&&(namesIndex=encodeInteger(writer,segment[4],namesIndex)))}}return writer.flush()}exports.decode=decode,exports.decodeGeneratedRanges=decodeGeneratedRanges,exports.decodeOriginalScopes=decodeOriginalScopes,exports.encode=encode,exports.encodeGeneratedRanges=encodeGeneratedRanges,exports.encodeOriginalScopes=encodeOriginalScopes,Object.defineProperty(exports,"__esModule",{value:!0})}(exports)},"./node_modules/.pnpm/@jridgewell+trace-mapping@0.3.25/node_modules/@jridgewell/trace-mapping/dist/trace-mapping.umd.js":function(__unused_webpack_module,exports,__webpack_require__){!function(exports,sourcemapCodec,resolveUri){"use strict";function resolve(input,base){return base&&!base.endsWith("/")&&(base+="/"),resolveUri(input,base)}function stripFilename(path){if(!path)return"";const index=path.lastIndexOf("/");return path.slice(0,index+1)}const COLUMN=0,SOURCES_INDEX=1,SOURCE_LINE=2,SOURCE_COLUMN=3,NAMES_INDEX=4,REV_GENERATED_LINE=1,REV_GENERATED_COLUMN=2;function maybeSort(mappings,owned){const unsortedIndex=nextUnsortedSegmentLine(mappings,0);if(unsortedIndex===mappings.length)return mappings;owned||(mappings=mappings.slice());for(let i=unsortedIndex;i<mappings.length;i=nextUnsortedSegmentLine(mappings,i+1))mappings[i]=sortSegments(mappings[i],owned);return mappings}function nextUnsortedSegmentLine(mappings,start){for(let i=start;i<mappings.length;i++)if(!isSorted(mappings[i]))return i;return mappings.length}function isSorted(line){for(let j=1;j<line.length;j++)if(line[j][COLUMN]<line[j-1][COLUMN])return!1;return!0}function sortSegments(line,owned){return owned||(line=line.slice()),line.sort(sortComparator)}function sortComparator(a,b){return a[COLUMN]-b[COLUMN]}let found=!1;function binarySearch(haystack,needle,low,high){for(;low<=high;){const mid=low+(high-low>>1),cmp=haystack[mid][COLUMN]-needle;if(0===cmp)return found=!0,mid;cmp<0?low=mid+1:high=mid-1}return found=!1,low-1}function upperBound(haystack,needle,index){for(let i=index+1;i<haystack.length&&haystack[i][COLUMN]===needle;index=i++);return index}function lowerBound(haystack,needle,index){for(let i=index-1;i>=0&&haystack[i][COLUMN]===needle;index=i--);return index}function memoizedState(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}function memoizedBinarySearch(haystack,needle,state,key){const{lastKey,lastNeedle,lastIndex}=state;let low=0,high=haystack.length-1;if(key===lastKey){if(needle===lastNeedle)return found=-1!==lastIndex&&haystack[lastIndex][COLUMN]===needle,lastIndex;needle>=lastNeedle?low=-1===lastIndex?0:lastIndex:high=lastIndex}return state.lastKey=key,state.lastNeedle=needle,state.lastIndex=binarySearch(haystack,needle,low,high)}function buildBySources(decoded,memos){const sources=memos.map(buildNullArray);for(let i=0;i<decoded.length;i++){const line=decoded[i];for(let j=0;j<line.length;j++){const seg=line[j];if(1===seg.length)continue;const sourceIndex=seg[SOURCES_INDEX],sourceLine=seg[SOURCE_LINE],sourceColumn=seg[SOURCE_COLUMN],originalSource=sources[sourceIndex],originalLine=originalSource[sourceLine]||(originalSource[sourceLine]=[]),memo=memos[sourceIndex];let index=upperBound(originalLine,sourceColumn,memoizedBinarySearch(originalLine,sourceColumn,memo,sourceLine));memo.lastIndex=++index,insert(originalLine,index,[sourceColumn,i,seg[COLUMN]])}}return sources}function insert(array,index,value){for(let i=array.length;i>index;i--)array[i]=array[i-1];array[index]=value}function buildNullArray(){return{__proto__:null}}const AnyMap=function(map,mapUrl){const parsed=parse(map);if(!("sections"in parsed))return new TraceMap(parsed,mapUrl);const mappings=[],sources=[],sourcesContent=[],names=[],ignoreList=[];return recurse(parsed,mapUrl,mappings,sources,sourcesContent,names,ignoreList,0,0,1/0,1/0),presortedDecodedMap({version:3,file:parsed.file,names,sources,sourcesContent,mappings,ignoreList})};function parse(map){return"string"==typeof map?JSON.parse(map):map}function recurse(input,mapUrl,mappings,sources,sourcesContent,names,ignoreList,lineOffset,columnOffset,stopLine,stopColumn){const{sections}=input;for(let i=0;i<sections.length;i++){const{map,offset}=sections[i];let sl=stopLine,sc=stopColumn;if(i+1<sections.length){const nextOffset=sections[i+1].offset;sl=Math.min(stopLine,lineOffset+nextOffset.line),sl===stopLine?sc=Math.min(stopColumn,columnOffset+nextOffset.column):sl<stopLine&&(sc=columnOffset+nextOffset.column)}addSection(map,mapUrl,mappings,sources,sourcesContent,names,ignoreList,lineOffset+offset.line,columnOffset+offset.column,sl,sc)}}function addSection(input,mapUrl,mappings,sources,sourcesContent,names,ignoreList,lineOffset,columnOffset,stopLine,stopColumn){const parsed=parse(input);if("sections"in parsed)return recurse(...arguments);const map=new TraceMap(parsed,mapUrl),sourcesOffset=sources.length,namesOffset=names.length,decoded=decodedMappings(map),{resolvedSources,sourcesContent:contents,ignoreList:ignores}=map;if(append(sources,resolvedSources),append(names,map.names),contents)append(sourcesContent,contents);else for(let i=0;i<resolvedSources.length;i++)sourcesContent.push(null);if(ignores)for(let i=0;i<ignores.length;i++)ignoreList.push(ignores[i]+sourcesOffset);for(let i=0;i<decoded.length;i++){const lineI=lineOffset+i;if(lineI>stopLine)return;const out=getLine(mappings,lineI),cOffset=0===i?columnOffset:0,line=decoded[i];for(let j=0;j<line.length;j++){const seg=line[j],column=cOffset+seg[COLUMN];if(lineI===stopLine&&column>=stopColumn)return;if(1===seg.length){out.push([column]);continue}const sourcesIndex=sourcesOffset+seg[SOURCES_INDEX],sourceLine=seg[SOURCE_LINE],sourceColumn=seg[SOURCE_COLUMN];out.push(4===seg.length?[column,sourcesIndex,sourceLine,sourceColumn]:[column,sourcesIndex,sourceLine,sourceColumn,namesOffset+seg[NAMES_INDEX]])}}}function append(arr,other){for(let i=0;i<other.length;i++)arr.push(other[i])}function getLine(arr,index){for(let i=arr.length;i<=index;i++)arr[i]=[];return arr[index]}const LINE_GTR_ZERO="`line` must be greater than 0 (lines start at line 1)",COL_GTR_EQ_ZERO="`column` must be greater than or equal to 0 (columns start at column 0)",LEAST_UPPER_BOUND=-1,GREATEST_LOWER_BOUND=1;class TraceMap{constructor(map,mapUrl){const isString="string"==typeof map;if(!isString&&map._decodedMemo)return map;const parsed=isString?JSON.parse(map):map,{version,file,names,sourceRoot,sources,sourcesContent}=parsed;this.version=version,this.file=file,this.names=names||[],this.sourceRoot=sourceRoot,this.sources=sources,this.sourcesContent=sourcesContent,this.ignoreList=parsed.ignoreList||parsed.x_google_ignoreList||void 0;const from=resolve(sourceRoot||"",stripFilename(mapUrl));this.resolvedSources=sources.map((s=>resolve(s||"",from)));const{mappings}=parsed;"string"==typeof mappings?(this._encoded=mappings,this._decoded=void 0):(this._encoded=void 0,this._decoded=maybeSort(mappings,isString)),this._decodedMemo=memoizedState(),this._bySources=void 0,this._bySourceMemos=void 0}}function cast(map){return map}function encodedMappings(map){var _a,_b;return null!==(_a=(_b=cast(map))._encoded)&&void 0!==_a?_a:_b._encoded=sourcemapCodec.encode(cast(map)._decoded)}function decodedMappings(map){var _a;return(_a=cast(map))._decoded||(_a._decoded=sourcemapCodec.decode(cast(map)._encoded))}function traceSegment(map,line,column){const decoded=decodedMappings(map);if(line>=decoded.length)return null;const segments=decoded[line],index=traceSegmentInternal(segments,cast(map)._decodedMemo,line,column,GREATEST_LOWER_BOUND);return-1===index?null:segments[index]}function originalPositionFor(map,needle){let{line,column,bias}=needle;if(line--,line<0)throw new Error(LINE_GTR_ZERO);if(column<0)throw new Error(COL_GTR_EQ_ZERO);const decoded=decodedMappings(map);if(line>=decoded.length)return OMapping(null,null,null,null);const segments=decoded[line],index=traceSegmentInternal(segments,cast(map)._decodedMemo,line,column,bias||GREATEST_LOWER_BOUND);if(-1===index)return OMapping(null,null,null,null);const segment=segments[index];if(1===segment.length)return OMapping(null,null,null,null);const{names,resolvedSources}=map;return OMapping(resolvedSources[segment[SOURCES_INDEX]],segment[SOURCE_LINE]+1,segment[SOURCE_COLUMN],5===segment.length?names[segment[NAMES_INDEX]]:null)}function generatedPositionFor(map,needle){const{source,line,column,bias}=needle;return generatedPosition(map,source,line,column,bias||GREATEST_LOWER_BOUND,!1)}function allGeneratedPositionsFor(map,needle){const{source,line,column,bias}=needle;return generatedPosition(map,source,line,column,bias||LEAST_UPPER_BOUND,!0)}function eachMapping(map,cb){const decoded=decodedMappings(map),{names,resolvedSources}=map;for(let i=0;i<decoded.length;i++){const line=decoded[i];for(let j=0;j<line.length;j++){const seg=line[j],generatedLine=i+1,generatedColumn=seg[0];let source=null,originalLine=null,originalColumn=null,name=null;1!==seg.length&&(source=resolvedSources[seg[1]],originalLine=seg[2]+1,originalColumn=seg[3]),5===seg.length&&(name=names[seg[4]]),cb({generatedLine,generatedColumn,source,originalLine,originalColumn,name})}}}function sourceIndex(map,source){const{sources,resolvedSources}=map;let index=sources.indexOf(source);return-1===index&&(index=resolvedSources.indexOf(source)),index}function sourceContentFor(map,source){const{sourcesContent}=map;if(null==sourcesContent)return null;const index=sourceIndex(map,source);return-1===index?null:sourcesContent[index]}function isIgnored(map,source){const{ignoreList}=map;if(null==ignoreList)return!1;const index=sourceIndex(map,source);return-1!==index&&ignoreList.includes(index)}function presortedDecodedMap(map,mapUrl){const tracer=new TraceMap(clone(map,[]),mapUrl);return cast(tracer)._decoded=map.mappings,tracer}function decodedMap(map){return clone(map,decodedMappings(map))}function encodedMap(map){return clone(map,encodedMappings(map))}function clone(map,mappings){return{version:map.version,file:map.file,names:map.names,sourceRoot:map.sourceRoot,sources:map.sources,sourcesContent:map.sourcesContent,mappings,ignoreList:map.ignoreList||map.x_google_ignoreList}}function OMapping(source,line,column,name){return{source,line,column,name}}function GMapping(line,column){return{line,column}}function traceSegmentInternal(segments,memo,line,column,bias){let index=memoizedBinarySearch(segments,column,memo,line);return found?index=(bias===LEAST_UPPER_BOUND?upperBound:lowerBound)(segments,column,index):bias===LEAST_UPPER_BOUND&&index++,-1===index||index===segments.length?-1:index}function sliceGeneratedPositions(segments,memo,line,column,bias){let min=traceSegmentInternal(segments,memo,line,column,GREATEST_LOWER_BOUND);if(found||bias!==LEAST_UPPER_BOUND||min++,-1===min||min===segments.length)return[];const matchedColumn=found?column:segments[min][COLUMN];found||(min=lowerBound(segments,matchedColumn,min));const max=upperBound(segments,matchedColumn,min),result=[];for(;min<=max;min++){const segment=segments[min];result.push(GMapping(segment[REV_GENERATED_LINE]+1,segment[REV_GENERATED_COLUMN]))}return result}function generatedPosition(map,source,line,column,bias,all){var _a;if(--line<0)throw new Error(LINE_GTR_ZERO);if(column<0)throw new Error(COL_GTR_EQ_ZERO);const{sources,resolvedSources}=map;let sourceIndex=sources.indexOf(source);if(-1===sourceIndex&&(sourceIndex=resolvedSources.indexOf(source)),-1===sourceIndex)return all?[]:GMapping(null,null);const segments=((_a=cast(map))._bySources||(_a._bySources=buildBySources(decodedMappings(map),cast(map)._bySourceMemos=sources.map(memoizedState))))[sourceIndex][line];if(null==segments)return all?[]:GMapping(null,null);const memo=cast(map)._bySourceMemos[sourceIndex];if(all)return sliceGeneratedPositions(segments,memo,line,column,bias);const index=traceSegmentInternal(segments,memo,line,column,bias);if(-1===index)return GMapping(null,null);const segment=segments[index];return GMapping(segment[REV_GENERATED_LINE]+1,segment[REV_GENERATED_COLUMN])}exports.AnyMap=AnyMap,exports.GREATEST_LOWER_BOUND=GREATEST_LOWER_BOUND,exports.LEAST_UPPER_BOUND=LEAST_UPPER_BOUND,exports.TraceMap=TraceMap,exports.allGeneratedPositionsFor=allGeneratedPositionsFor,exports.decodedMap=decodedMap,exports.decodedMappings=decodedMappings,exports.eachMapping=eachMapping,exports.encodedMap=encodedMap,exports.encodedMappings=encodedMappings,exports.generatedPositionFor=generatedPositionFor,exports.isIgnored=isIgnored,exports.originalPositionFor=originalPositionFor,exports.presortedDecodedMap=presortedDecodedMap,exports.sourceContentFor=sourceContentFor,exports.traceSegment=traceSegment}(exports,__webpack_require__("./node_modules/.pnpm/@jridgewell+sourcemap-codec@1.5.0/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.umd.js"),__webpack_require__("./node_modules/.pnpm/@jridgewell+resolve-uri@3.1.2/node_modules/@jridgewell/resolve-uri/dist/resolve-uri.umd.js"))},"./node_modules/.pnpm/babel-plugin-parameter-decorator@1.0.16/node_modules/babel-plugin-parameter-decorator/lib/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";var _path=__webpack_require__("path");function isInType(path){switch(path.parent.type){case"TSTypeReference":case"TSQualifiedName":case"TSExpressionWithTypeArguments":case"TSTypeQuery":return!0;default:return!1}}module.exports=function(_ref){var types=_ref.types,decoratorExpressionForConstructor=function(decorator,param){return function(className){var resultantDecorator=types.callExpression(decorator.expression,[types.Identifier(className),types.Identifier("undefined"),types.NumericLiteral(param.key)]),resultantDecoratorWithFallback=types.logicalExpression("||",resultantDecorator,types.Identifier(className)),assignment=types.assignmentExpression("=",types.Identifier(className),resultantDecoratorWithFallback);return types.expressionStatement(assignment)}},decoratorExpressionForMethod=function(decorator,param){return function(className,functionName){var resultantDecorator=types.callExpression(decorator.expression,[types.Identifier("".concat(className,".prototype")),types.StringLiteral(functionName),types.NumericLiteral(param.key)]);return types.expressionStatement(resultantDecorator)}};return{visitor:{Program:function(path,state){var extension=(0,_path.extname)(state.file.opts.filename);".ts"!==extension&&".tsx"!==extension||function(){var decorators=Object.create(null);path.node.body.filter((function(it){var type=it.type,declaration=it.declaration;switch(type){case"ClassDeclaration":return!0;case"ExportNamedDeclaration":case"ExportDefaultDeclaration":return declaration&&"ClassDeclaration"===declaration.type;default:return!1}})).map((function(it){return"ClassDeclaration"===it.type?it:it.declaration})).forEach((function(clazz){clazz.body.body.forEach((function(body){(body.params||[]).forEach((function(param){(param.decorators||[]).forEach((function(decorator){decorator.expression.callee?decorators[decorator.expression.callee.name]=decorator:decorators[decorator.expression.name]=decorator}))}))}))}));var _iteratorNormalCompletion=!0,_didIteratorError=!1,_iteratorError=void 0;try{for(var _step,_iterator=path.get("body")[Symbol.iterator]();!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=!0){var stmt=_step.value;if("ImportDeclaration"===stmt.node.type){if(0===stmt.node.specifiers.length)continue;var _iteratorNormalCompletion2=!0,_didIteratorError2=!1,_iteratorError2=void 0;try{for(var _step2,_loop=function(){var specifier=_step2.value,binding=stmt.scope.getBinding(specifier.local.name);binding.referencePaths.length?binding.referencePaths.reduce((function(prev,next){return prev||isInType(next)}),!1)&&Object.keys(decorators).forEach((function(k){var decorator=decorators[k];(decorator.expression.arguments||[]).forEach((function(arg){arg.name===specifier.local.name&&binding.referencePaths.push({parent:decorator.expression})}))})):decorators[specifier.local.name]&&binding.referencePaths.push({parent:decorators[specifier.local.name]})},_iterator2=stmt.node.specifiers[Symbol.iterator]();!(_iteratorNormalCompletion2=(_step2=_iterator2.next()).done);_iteratorNormalCompletion2=!0)_loop()}catch(err){_didIteratorError2=!0,_iteratorError2=err}finally{try{_iteratorNormalCompletion2||null==_iterator2.return||_iterator2.return()}finally{if(_didIteratorError2)throw _iteratorError2}}}}}catch(err){_didIteratorError=!0,_iteratorError=err}finally{try{_iteratorNormalCompletion||null==_iterator.return||_iterator.return()}finally{if(_didIteratorError)throw _iteratorError}}}()},Function:function(path){var functionName="";path.node.id?functionName=path.node.id.name:path.node.key&&(functionName=path.node.key.name),(path.get("params")||[]).slice().forEach((function(param){var decorators=param.node.decorators||[],transformable=decorators.length;if(decorators.slice().forEach((function(decorator){if("ClassMethod"===path.type){var classIdentifier,parentNode=path.parentPath.parentPath,classDeclaration=path.findParent((function(p){return"ClassDeclaration"===p.type}));if(classDeclaration?classIdentifier=classDeclaration.node.id.name:(parentNode.insertAfter(null),classIdentifier=function(path){var assignment=path.findParent((function(p){return"AssignmentExpression"===p.node.type}));return"SequenceExpression"===assignment.node.right.type?assignment.node.right.expressions[1].name:"ClassExpression"===assignment.node.right.type?assignment.node.left.name:null}(path)),"constructor"===functionName){var expression=decoratorExpressionForConstructor(decorator,param)(classIdentifier);parentNode.insertAfter(expression)}else{var _expression=decoratorExpressionForMethod(decorator,param)(classIdentifier,functionName);parentNode.insertAfter(_expression)}}else{var className=path.findParent((function(p){return"VariableDeclarator"===p.node.type})).node.id.name;if(functionName===className){var _expression2=decoratorExpressionForConstructor(decorator,param)(className);if("body"===path.parentKey)path.insertAfter(_expression2);else path.findParent((function(p){return"body"===p.parentKey})).insertAfter(_expression2)}else{var classParent=path.findParent((function(p){return"CallExpression"===p.node.type})),_expression3=decoratorExpressionForMethod(decorator,param)(className,functionName);classParent.insertAfter(_expression3)}}})),transformable){var replacement=function(path){switch(path.node.type){case"ObjectPattern":return types.ObjectPattern(path.node.properties);case"AssignmentPattern":return types.AssignmentPattern(path.node.left,path.node.right);case"TSParameterProperty":return types.Identifier(path.node.parameter.name);default:return types.Identifier(path.node.name)}}(param);param.replaceWith(replacement)}}))}}}}},"./node_modules/.pnpm/convert-source-map@2.0.0/node_modules/convert-source-map/index.js":(__unused_webpack_module,exports)=>{"use strict";var decodeBase64;function Converter(sm,opts){(opts=opts||{}).hasComment&&(sm=function(sm){return sm.split(",").pop()}(sm)),"base64"===opts.encoding?sm=decodeBase64(sm):"uri"===opts.encoding&&(sm=decodeURIComponent(sm)),(opts.isJSON||opts.encoding)&&(sm=JSON.parse(sm)),this.sourcemap=sm}function makeConverter(sm){return new Converter(sm,{isJSON:!0})}Object.defineProperty(exports,"commentRegex",{get:function(){return/^\s*?\/[\/\*][@#]\s+?sourceMappingURL=data:(((?:application|text)\/json)(?:;charset=([^;,]+?)?)?)?(?:;(base64))?,(.*?)$/gm}}),Object.defineProperty(exports,"mapFileCommentRegex",{get:function(){return/(?:\/\/[@#][ \t]+?sourceMappingURL=([^\s'"`]+?)[ \t]*?$)|(?:\/\*[@#][ \t]+sourceMappingURL=([^*]+?)[ \t]*?(?:\*\/){1}[ \t]*?$)/gm}}),decodeBase64="undefined"!=typeof Buffer?"function"==typeof Buffer.from?function(base64){return Buffer.from(base64,"base64").toString()}:function(base64){if("number"==typeof value)throw new TypeError("The value to decode must not be of type number.");return new Buffer(base64,"base64").toString()}:function(base64){return decodeURIComponent(escape(atob(base64)))},Converter.prototype.toJSON=function(space){return JSON.stringify(this.sourcemap,null,space)},"undefined"!=typeof Buffer?"function"==typeof Buffer.from?Converter.prototype.toBase64=function(){var json=this.toJSON();return Buffer.from(json,"utf8").toString("base64")}:Converter.prototype.toBase64=function(){var json=this.toJSON();if("number"==typeof json)throw new TypeError("The json to encode must not be of type number.");return new Buffer(json,"utf8").toString("base64")}:Converter.prototype.toBase64=function(){var json=this.toJSON();return btoa(unescape(encodeURIComponent(json)))},Converter.prototype.toURI=function(){var json=this.toJSON();return encodeURIComponent(json)},Converter.prototype.toComment=function(options){var encoding,content,data;return null!=options&&"uri"===options.encoding?(encoding="",content=this.toURI()):(encoding=";base64",content=this.toBase64()),data="sourceMappingURL=data:application/json;charset=utf-8"+encoding+","+content,null!=options&&options.multiline?"/*# "+data+" */":"//# "+data},Converter.prototype.toObject=function(){return JSON.parse(this.toJSON())},Converter.prototype.addProperty=function(key,value){if(this.sourcemap.hasOwnProperty(key))throw new Error('property "'+key+'" already exists on the sourcemap, use set property instead');return this.setProperty(key,value)},Converter.prototype.setProperty=function(key,value){return this.sourcemap[key]=value,this},Converter.prototype.getProperty=function(key){return this.sourcemap[key]},exports.fromObject=function(obj){return new Converter(obj)},exports.fromJSON=function(json){return new Converter(json,{isJSON:!0})},exports.fromURI=function(uri){return new Converter(uri,{encoding:"uri"})},exports.fromBase64=function(base64){return new Converter(base64,{encoding:"base64"})},exports.fromComment=function(comment){var m;return new Converter(comment=comment.replace(/^\/\*/g,"//").replace(/\*\/$/g,""),{encoding:(m=exports.commentRegex.exec(comment))&&m[4]||"uri",hasComment:!0})},exports.fromMapFileComment=function(comment,read){if("string"==typeof read)throw new Error("String directory paths are no longer supported with `fromMapFileComment`\nPlease review the Upgrading documentation at https://github.com/thlorenz/convert-source-map#upgrading");var sm=function(sm,read){var r=exports.mapFileCommentRegex.exec(sm),filename=r[1]||r[2];try{return null!=(sm=read(filename))&&"function"==typeof sm.catch?sm.catch(throwError):sm}catch(e){throwError(e)}function throwError(e){throw new Error("An error occurred while trying to read the map file at "+filename+"\n"+e.stack)}}(comment,read);return null!=sm&&"function"==typeof sm.then?sm.then(makeConverter):makeConverter(sm)},exports.fromSource=function(content){var m=content.match(exports.commentRegex);return m?exports.fromComment(m.pop()):null},exports.fromMapFileSource=function(content,read){if("string"==typeof read)throw new Error("String directory paths are no longer supported with `fromMapFileSource`\nPlease review the Upgrading documentation at https://github.com/thlorenz/convert-source-map#upgrading");var m=content.match(exports.mapFileCommentRegex);return m?exports.fromMapFileComment(m.pop(),read):null},exports.removeComments=function(src){return src.replace(exports.commentRegex,"")},exports.removeMapFileComments=function(src){return src.replace(exports.mapFileCommentRegex,"")},exports.generateMapFileComment=function(file,options){var data="sourceMappingURL="+file;return options&&options.multiline?"/*# "+data+" */":"//# "+data}},"./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/browser.js":(module,exports,__webpack_require__)=>{exports.formatArgs=function(args){if(args[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+args[0]+(this.useColors?"%c ":" ")+"+"+module.exports.humanize(this.diff),!this.useColors)return;const c="color: "+this.color;args.splice(1,0,c,"color: inherit");let index=0,lastC=0;args[0].replace(/%[a-zA-Z%]/g,(match=>{"%%"!==match&&(index++,"%c"===match&&(lastC=index))})),args.splice(lastC,0,c)},exports.save=function(namespaces){try{namespaces?exports.storage.setItem("debug",namespaces):exports.storage.removeItem("debug")}catch(error){}},exports.load=function(){let r;try{r=exports.storage.getItem("debug")}catch(error){}!r&&"undefined"!=typeof process&&"env"in process&&(r=process.env.DEBUG);return r},exports.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let m;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(m=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(m[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},exports.storage=function(){try{return localStorage}catch(error){}}(),exports.destroy=(()=>{let warned=!1;return()=>{warned||(warned=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),exports.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],exports.log=console.debug||console.log||(()=>{}),module.exports=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/common.js")(exports);const{formatters}=module.exports;formatters.j=function(v){try{return JSON.stringify(v)}catch(error){return"[UnexpectedJSONParseError]: "+error.message}}},"./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/common.js":(module,__unused_webpack_exports,__webpack_require__)=>{module.exports=function(env){function createDebug(namespace){let prevTime,namespacesCache,enabledCache,enableOverride=null;function debug(...args){if(!debug.enabled)return;const self=debug,curr=Number(new Date),ms=curr-(prevTime||curr);self.diff=ms,self.prev=prevTime,self.curr=curr,prevTime=curr,args[0]=createDebug.coerce(args[0]),"string"!=typeof args[0]&&args.unshift("%O");let index=0;args[0]=args[0].replace(/%([a-zA-Z%])/g,((match,format)=>{if("%%"===match)return"%";index++;const formatter=createDebug.formatters[format];if("function"==typeof formatter){const val=args[index];match=formatter.call(self,val),args.splice(index,1),index--}return match})),createDebug.formatArgs.call(self,args);(self.log||createDebug.log).apply(self,args)}return debug.namespace=namespace,debug.useColors=createDebug.useColors(),debug.color=createDebug.selectColor(namespace),debug.extend=extend,debug.destroy=createDebug.destroy,Object.defineProperty(debug,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==enableOverride?enableOverride:(namespacesCache!==createDebug.namespaces&&(namespacesCache=createDebug.namespaces,enabledCache=createDebug.enabled(namespace)),enabledCache),set:v=>{enableOverride=v}}),"function"==typeof createDebug.init&&createDebug.init(debug),debug}function extend(namespace,delimiter){const newDebug=createDebug(this.namespace+(void 0===delimiter?":":delimiter)+namespace);return newDebug.log=this.log,newDebug}function matchesTemplate(search,template){let searchIndex=0,templateIndex=0,starIndex=-1,matchIndex=0;for(;searchIndex<search.length;)if(templateIndex<template.length&&(template[templateIndex]===search[searchIndex]||"*"===template[templateIndex]))"*"===template[templateIndex]?(starIndex=templateIndex,matchIndex=searchIndex,templateIndex++):(searchIndex++,templateIndex++);else{if(-1===starIndex)return!1;templateIndex=starIndex+1,matchIndex++,searchIndex=matchIndex}for(;templateIndex<template.length&&"*"===template[templateIndex];)templateIndex++;return templateIndex===template.length}return createDebug.debug=createDebug,createDebug.default=createDebug,createDebug.coerce=function(val){if(val instanceof Error)return val.stack||val.message;return val},createDebug.disable=function(){const namespaces=[...createDebug.names,...createDebug.skips.map((namespace=>"-"+namespace))].join(",");return createDebug.enable(""),namespaces},createDebug.enable=function(namespaces){createDebug.save(namespaces),createDebug.namespaces=namespaces,createDebug.names=[],createDebug.skips=[];const split=("string"==typeof namespaces?namespaces:"").trim().replace(" ",",").split(",").filter(Boolean);for(const ns of split)"-"===ns[0]?createDebug.skips.push(ns.slice(1)):createDebug.names.push(ns)},createDebug.enabled=function(name){for(const skip of createDebug.skips)if(matchesTemplate(name,skip))return!1;for(const ns of createDebug.names)if(matchesTemplate(name,ns))return!0;return!1},createDebug.humanize=__webpack_require__("./node_modules/.pnpm/ms@2.1.3/node_modules/ms/index.js"),createDebug.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(env).forEach((key=>{createDebug[key]=env[key]})),createDebug.names=[],createDebug.skips=[],createDebug.formatters={},createDebug.selectColor=function(namespace){let hash=0;for(let i=0;i<namespace.length;i++)hash=(hash<<5)-hash+namespace.charCodeAt(i),hash|=0;return createDebug.colors[Math.abs(hash)%createDebug.colors.length]},createDebug.enable(createDebug.load()),createDebug}},"./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?module.exports=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/browser.js"):module.exports=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/node.js")},"./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/node.js":(module,exports,__webpack_require__)=>{const tty=__webpack_require__("tty"),util=__webpack_require__("util");exports.init=function(debug){debug.inspectOpts={};const keys=Object.keys(exports.inspectOpts);for(let i=0;i<keys.length;i++)debug.inspectOpts[keys[i]]=exports.inspectOpts[keys[i]]},exports.log=function(...args){return process.stderr.write(util.formatWithOptions(exports.inspectOpts,...args)+"\n")},exports.formatArgs=function(args){const{namespace:name,useColors}=this;if(useColors){const c=this.color,colorCode="[3"+(c<8?c:"8;5;"+c),prefix=`  ${colorCode};1m${name} [0m`;args[0]=prefix+args[0].split("\n").join("\n"+prefix),args.push(colorCode+"m+"+module.exports.humanize(this.diff)+"[0m")}else args[0]=function(){if(exports.inspectOpts.hideDate)return"";return(new Date).toISOString()+" "}()+name+" "+args[0]},exports.save=function(namespaces){namespaces?process.env.DEBUG=namespaces:delete process.env.DEBUG},exports.load=function(){return process.env.DEBUG},exports.useColors=function(){return"colors"in exports.inspectOpts?Boolean(exports.inspectOpts.colors):tty.isatty(process.stderr.fd)},exports.destroy=util.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),exports.colors=[6,2,3,4,5,1];try{const supportsColor=__webpack_require__("./node_modules/.pnpm/supports-color@7.2.0/node_modules/supports-color/index.js");supportsColor&&(supportsColor.stderr||supportsColor).level>=2&&(exports.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(error){}exports.inspectOpts=Object.keys(process.env).filter((key=>/^debug_/i.test(key))).reduce(((obj,key)=>{const prop=key.substring(6).toLowerCase().replace(/_([a-z])/g,((_,k)=>k.toUpperCase()));let val=process.env[key];return val=!!/^(yes|on|true|enabled)$/i.test(val)||!/^(no|off|false|disabled)$/i.test(val)&&("null"===val?null:Number(val)),obj[prop]=val,obj}),{}),module.exports=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/common.js")(exports);const{formatters}=module.exports;formatters.o=function(v){return this.inspectOpts.colors=this.useColors,util.inspect(v,this.inspectOpts).split("\n").map((str=>str.trim())).join(" ")},formatters.O=function(v){return this.inspectOpts.colors=this.useColors,util.inspect(v,this.inspectOpts)}},"./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js":module=>{"use strict";const GENSYNC_START=Symbol.for("gensync:v1:start"),GENSYNC_SUSPEND=Symbol.for("gensync:v1:suspend");function assertTypeof(type,name,value,allowUndefined){if(typeof value===type||allowUndefined&&void 0===value)return;let msg;throw msg=allowUndefined?`Expected opts.${name} to be either a ${type}, or undefined.`:`Expected opts.${name} to be a ${type}.`,makeError(msg,"GENSYNC_OPTIONS_ERROR")}function makeError(msg,code){return Object.assign(new Error(msg),{code})}function buildOperation({name,arity,sync,async}){return setFunctionMetadata(name,arity,(function*(...args){const resume=yield GENSYNC_START;if(!resume){return sync.call(this,args)}let result;try{async.call(this,args,(value=>{result||(result={value},resume())}),(err=>{result||(result={err},resume())}))}catch(err){result={err},resume()}if(yield GENSYNC_SUSPEND,result.hasOwnProperty("err"))throw result.err;return result.value}))}function evaluateSync(gen){let value;for(;!({value}=gen.next()).done;)assertStart(value,gen);return value}function evaluateAsync(gen,resolve,reject){!function step(){try{let value;for(;!({value}=gen.next()).done;){assertStart(value,gen);let sync=!0,didSyncResume=!1;const out=gen.next((()=>{sync?didSyncResume=!0:step()}));if(sync=!1,assertSuspend(out,gen),!didSyncResume)return}return resolve(value)}catch(err){return reject(err)}}()}function assertStart(value,gen){value!==GENSYNC_START&&throwError(gen,makeError(`Got unexpected yielded value in gensync generator: ${JSON.stringify(value)}. Did you perhaps mean to use 'yield*' instead of 'yield'?`,"GENSYNC_EXPECTED_START"))}function assertSuspend({value,done},gen){(done||value!==GENSYNC_SUSPEND)&&throwError(gen,makeError(done?"Unexpected generator completion. If you get this, it is probably a gensync bug.":`Expected GENSYNC_SUSPEND, got ${JSON.stringify(value)}. If you get this, it is probably a gensync bug.`,"GENSYNC_EXPECTED_SUSPEND"))}function throwError(gen,err){throw gen.throw&&gen.throw(err),err}function setFunctionMetadata(name,arity,fn){if("string"==typeof name){const nameDesc=Object.getOwnPropertyDescriptor(fn,"name");nameDesc&&!nameDesc.configurable||Object.defineProperty(fn,"name",Object.assign(nameDesc||{},{configurable:!0,value:name}))}if("number"==typeof arity){const lengthDesc=Object.getOwnPropertyDescriptor(fn,"length");lengthDesc&&!lengthDesc.configurable||Object.defineProperty(fn,"length",Object.assign(lengthDesc||{},{configurable:!0,value:arity}))}return fn}module.exports=Object.assign((function(optsOrFn){let genFn=optsOrFn;return genFn="function"!=typeof optsOrFn?function({name,arity,sync,async,errback}){if(assertTypeof("string","name",name,!0),assertTypeof("number","arity",arity,!0),assertTypeof("function","sync",sync),assertTypeof("function","async",async,!0),assertTypeof("function","errback",errback,!0),async&&errback)throw makeError("Expected one of either opts.async or opts.errback, but got _both_.","GENSYNC_OPTIONS_ERROR");if("string"!=typeof name){let fnName;errback&&errback.name&&"errback"!==errback.name&&(fnName=errback.name),async&&async.name&&"async"!==async.name&&(fnName=async.name.replace(/Async$/,"")),sync&&sync.name&&"sync"!==sync.name&&(fnName=sync.name.replace(/Sync$/,"")),"string"==typeof fnName&&(name=fnName)}"number"!=typeof arity&&(arity=sync.length);return buildOperation({name,arity,sync:function(args){return sync.apply(this,args)},async:function(args,resolve,reject){async?async.apply(this,args).then(resolve,reject):errback?errback.call(this,...args,((err,value)=>{null==err?resolve(value):reject(err)})):resolve(sync.apply(this,args))}})}(optsOrFn):function(genFn){return setFunctionMetadata(genFn.name,genFn.length,(function(...args){return genFn.apply(this,args)}))}(optsOrFn),Object.assign(genFn,function(genFn){const fns={sync:function(...args){return evaluateSync(genFn.apply(this,args))},async:function(...args){return new Promise(((resolve,reject)=>{evaluateAsync(genFn.apply(this,args),resolve,reject)}))},errback:function(...args){const cb=args.pop();if("function"!=typeof cb)throw makeError("Asynchronous function called without callback","GENSYNC_ERRBACK_NO_CALLBACK");let gen;try{gen=genFn.apply(this,args)}catch(err){return void cb(err)}evaluateAsync(gen,(val=>cb(void 0,val)),(err=>cb(err)))}};return fns}(genFn))}),{all:buildOperation({name:"all",arity:1,sync:function(args){return Array.from(args[0]).map((item=>evaluateSync(item)))},async:function(args,resolve,reject){const items=Array.from(args[0]);if(0===items.length)return void Promise.resolve().then((()=>resolve([])));let count=0;const results=items.map((()=>{}));items.forEach(((item,i)=>{evaluateAsync(item,(val=>{results[i]=val,count+=1,count===results.length&&resolve(results)}),reject)}))}}),race:buildOperation({name:"race",arity:1,sync:function(args){const items=Array.from(args[0]);if(0===items.length)throw makeError("Must race at least 1 item","GENSYNC_RACE_NONEMPTY");return evaluateSync(items[0])},async:function(args,resolve,reject){const items=Array.from(args[0]);if(0===items.length)throw makeError("Must race at least 1 item","GENSYNC_RACE_NONEMPTY");for(const item of items)evaluateAsync(item,resolve,reject)}})})},"./node_modules/.pnpm/globals@11.12.0/node_modules/globals/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";module.exports=__webpack_require__("./node_modules/.pnpm/globals@11.12.0/node_modules/globals/globals.json")},"./node_modules/.pnpm/has-flag@4.0.0/node_modules/has-flag/index.js":module=>{"use strict";module.exports=(flag,argv=process.argv)=>{const prefix=flag.startsWith("-")?"":1===flag.length?"-":"--",position=argv.indexOf(prefix+flag),terminatorPosition=argv.indexOf("--");return-1!==position&&(-1===terminatorPosition||position<terminatorPosition)}},"./node_modules/.pnpm/jsesc@3.1.0/node_modules/jsesc/jsesc.js":module=>{"use strict";const object={},hasOwnProperty=object.hasOwnProperty,forOwn=(object,callback)=>{for(const key in object)hasOwnProperty.call(object,key)&&callback(key,object[key])},fourHexEscape=hex=>"\\u"+("0000"+hex).slice(-4),hexadecimal=(code,lowercase)=>{let hexadecimal=code.toString(16);return lowercase?hexadecimal:hexadecimal.toUpperCase()},toString=object.toString,isArray=Array.isArray,isBigInt=value=>"bigint"==typeof value,singleEscapes={"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t"},regexSingleEscape=/[\\\b\f\n\r\t]/,regexDigit=/[0-9]/,regexWhitespace=/[\xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,escapeEverythingRegex=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^]/g,escapeNonAsciiRegex=/([\uD800-\uDBFF][\uDC00-\uDFFF])|([\uD800-\uDFFF])|(['"`])|[^ !#-&\(-\[\]-_a-~]/g,jsesc=(argument,options)=>{const increaseIndentation=()=>{oldIndent=indent,++options.indentLevel,indent=options.indent.repeat(options.indentLevel)},defaults={escapeEverything:!1,minimal:!1,isScriptContext:!1,quotes:"single",wrap:!1,es6:!1,json:!1,compact:!0,lowercaseHex:!1,numbers:"decimal",indent:"\t",indentLevel:0,__inline1__:!1,__inline2__:!1},json=options&&options.json;var destination,source;json&&(defaults.quotes="double",defaults.wrap=!0),destination=defaults,"single"!=(options=(source=options)?(forOwn(source,((key,value)=>{destination[key]=value})),destination):destination).quotes&&"double"!=options.quotes&&"backtick"!=options.quotes&&(options.quotes="single");const quote="double"==options.quotes?'"':"backtick"==options.quotes?"`":"'",compact=options.compact,lowercaseHex=options.lowercaseHex;let indent=options.indent.repeat(options.indentLevel),oldIndent="";const inline1=options.__inline1__,inline2=options.__inline2__,newLine=compact?"":"\n";let result,isEmpty=!0;const useBinNumbers="binary"==options.numbers,useOctNumbers="octal"==options.numbers,useDecNumbers="decimal"==options.numbers,useHexNumbers="hexadecimal"==options.numbers;if(json&&argument&&(value=>"function"==typeof value)(argument.toJSON)&&(argument=argument.toJSON()),!(value=>"string"==typeof value||"[object String]"==toString.call(value))(argument)){if((value=>"[object Map]"==toString.call(value))(argument))return 0==argument.size?"new Map()":(compact||(options.__inline1__=!0,options.__inline2__=!1),"new Map("+jsesc(Array.from(argument),options)+")");if((value=>"[object Set]"==toString.call(value))(argument))return 0==argument.size?"new Set()":"new Set("+jsesc(Array.from(argument),options)+")";if((value=>"function"==typeof Buffer&&Buffer.isBuffer(value))(argument))return 0==argument.length?"Buffer.from([])":"Buffer.from("+jsesc(Array.from(argument),options)+")";if(isArray(argument))return result=[],options.wrap=!0,inline1&&(options.__inline1__=!1,options.__inline2__=!0),inline2||increaseIndentation(),((array,callback)=>{const length=array.length;let index=-1;for(;++index<length;)callback(array[index])})(argument,(value=>{isEmpty=!1,inline2&&(options.__inline2__=!1),result.push((compact||inline2?"":indent)+jsesc(value,options))})),isEmpty?"[]":inline2?"["+result.join(", ")+"]":"["+newLine+result.join(","+newLine)+newLine+(compact?"":oldIndent)+"]";if((value=>"number"==typeof value||"[object Number]"==toString.call(value))(argument)||isBigInt(argument)){if(json)return JSON.stringify(Number(argument));let result;if(useDecNumbers)result=String(argument);else if(useHexNumbers){let hexadecimal=argument.toString(16);lowercaseHex||(hexadecimal=hexadecimal.toUpperCase()),result="0x"+hexadecimal}else useBinNumbers?result="0b"+argument.toString(2):useOctNumbers&&(result="0o"+argument.toString(8));return isBigInt(argument)?result+"n":result}return isBigInt(argument)?json?JSON.stringify(Number(argument)):argument+"n":(value=>"[object Object]"==toString.call(value))(argument)?(result=[],options.wrap=!0,increaseIndentation(),forOwn(argument,((key,value)=>{isEmpty=!1,result.push((compact?"":indent)+jsesc(key,options)+":"+(compact?"":" ")+jsesc(value,options))})),isEmpty?"{}":"{"+newLine+result.join(","+newLine)+newLine+(compact?"":oldIndent)+"}"):json?JSON.stringify(argument)||"null":String(argument)}const regex=options.escapeEverything?escapeEverythingRegex:escapeNonAsciiRegex;return result=argument.replace(regex,((char,pair,lone,quoteChar,index,string)=>{if(pair){if(options.minimal)return pair;const first=pair.charCodeAt(0),second=pair.charCodeAt(1);if(options.es6){return"\\u{"+hexadecimal(1024*(first-55296)+second-56320+65536,lowercaseHex)+"}"}return fourHexEscape(hexadecimal(first,lowercaseHex))+fourHexEscape(hexadecimal(second,lowercaseHex))}if(lone)return fourHexEscape(hexadecimal(lone.charCodeAt(0),lowercaseHex));if("\0"==char&&!json&&!regexDigit.test(string.charAt(index+1)))return"\\0";if(quoteChar)return quoteChar==quote||options.escapeEverything?"\\"+quoteChar:quoteChar;if(regexSingleEscape.test(char))return singleEscapes[char];if(options.minimal&&!regexWhitespace.test(char))return char;const hex=hexadecimal(char.charCodeAt(0),lowercaseHex);return json||hex.length>2?fourHexEscape(hex):"\\x"+("00"+hex).slice(-2)})),"`"==quote&&(result=result.replace(/\$\{/g,"\\${")),options.isScriptContext&&(result=result.replace(/<\/(script|style)/gi,"<\\/$1").replace(/<!--/g,json?"\\u003C!--":"\\x3C!--")),options.wrap&&(result=quote+result+quote),result};jsesc.version="3.0.2",module.exports=jsesc},"./node_modules/.pnpm/mlly@1.7.3/node_modules/mlly/dist lazy recursive":module=>{function webpackEmptyAsyncContext(req){return Promise.resolve().then((()=>{var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}))}webpackEmptyAsyncContext.keys=()=>[],webpackEmptyAsyncContext.resolve=webpackEmptyAsyncContext,webpackEmptyAsyncContext.id="./node_modules/.pnpm/mlly@1.7.3/node_modules/mlly/dist lazy recursive",module.exports=webpackEmptyAsyncContext},"./node_modules/.pnpm/ms@2.1.3/node_modules/ms/index.js":module=>{var s=1e3,m=60*s,h=60*m,d=24*h,w=7*d,y=365.25*d;function plural(ms,msAbs,n,name){var isPlural=msAbs>=1.5*n;return Math.round(ms/n)+" "+name+(isPlural?"s":"")}module.exports=function(val,options){options=options||{};var type=typeof val;if("string"===type&&val.length>0)return function(str){if((str=String(str)).length>100)return;var match=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(str);if(!match)return;var n=parseFloat(match[1]);switch((match[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return n*y;case"weeks":case"week":case"w":return n*w;case"days":case"day":case"d":return n*d;case"hours":case"hour":case"hrs":case"hr":case"h":return n*h;case"minutes":case"minute":case"mins":case"min":case"m":return n*m;case"seconds":case"second":case"secs":case"sec":case"s":return n*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}(val);if("number"===type&&isFinite(val))return options.long?function(ms){var msAbs=Math.abs(ms);if(msAbs>=d)return plural(ms,msAbs,d,"day");if(msAbs>=h)return plural(ms,msAbs,h,"hour");if(msAbs>=m)return plural(ms,msAbs,m,"minute");if(msAbs>=s)return plural(ms,msAbs,s,"second");return ms+" ms"}(val):function(ms){var msAbs=Math.abs(ms);if(msAbs>=d)return Math.round(ms/d)+"d";if(msAbs>=h)return Math.round(ms/h)+"h";if(msAbs>=m)return Math.round(ms/m)+"m";if(msAbs>=s)return Math.round(ms/s)+"s";return ms+"ms"}(val);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(val))}},"./node_modules/.pnpm/semver@6.3.1/node_modules/semver/semver.js":(module,exports)=>{var debug;exports=module.exports=SemVer,debug="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?function(){var args=Array.prototype.slice.call(arguments,0);args.unshift("SEMVER"),console.log.apply(console,args)}:function(){},exports.SEMVER_SPEC_VERSION="2.0.0";var MAX_LENGTH=256,MAX_SAFE_INTEGER=Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_BUILD_LENGTH=MAX_LENGTH-6,re=exports.re=[],safeRe=exports.safeRe=[],src=exports.src=[],t=exports.tokens={},R=0;function tok(n){t[n]=R++}var safeRegexReplacements=[["\\s",1],["\\d",MAX_LENGTH],["[a-zA-Z0-9-]",MAX_SAFE_BUILD_LENGTH]];function makeSafeRe(value){for(var i=0;i<safeRegexReplacements.length;i++){var token=safeRegexReplacements[i][0],max=safeRegexReplacements[i][1];value=value.split(token+"*").join(token+"{0,"+max+"}").split(token+"+").join(token+"{1,"+max+"}")}return value}tok("NUMERICIDENTIFIER"),src[t.NUMERICIDENTIFIER]="0|[1-9]\\d*",tok("NUMERICIDENTIFIERLOOSE"),src[t.NUMERICIDENTIFIERLOOSE]="\\d+",tok("NONNUMERICIDENTIFIER"),src[t.NONNUMERICIDENTIFIER]="\\d*[a-zA-Z-][a-zA-Z0-9-]*",tok("MAINVERSION"),src[t.MAINVERSION]="("+src[t.NUMERICIDENTIFIER]+")\\.("+src[t.NUMERICIDENTIFIER]+")\\.("+src[t.NUMERICIDENTIFIER]+")",tok("MAINVERSIONLOOSE"),src[t.MAINVERSIONLOOSE]="("+src[t.NUMERICIDENTIFIERLOOSE]+")\\.("+src[t.NUMERICIDENTIFIERLOOSE]+")\\.("+src[t.NUMERICIDENTIFIERLOOSE]+")",tok("PRERELEASEIDENTIFIER"),src[t.PRERELEASEIDENTIFIER]="(?:"+src[t.NUMERICIDENTIFIER]+"|"+src[t.NONNUMERICIDENTIFIER]+")",tok("PRERELEASEIDENTIFIERLOOSE"),src[t.PRERELEASEIDENTIFIERLOOSE]="(?:"+src[t.NUMERICIDENTIFIERLOOSE]+"|"+src[t.NONNUMERICIDENTIFIER]+")",tok("PRERELEASE"),src[t.PRERELEASE]="(?:-("+src[t.PRERELEASEIDENTIFIER]+"(?:\\."+src[t.PRERELEASEIDENTIFIER]+")*))",tok("PRERELEASELOOSE"),src[t.PRERELEASELOOSE]="(?:-?("+src[t.PRERELEASEIDENTIFIERLOOSE]+"(?:\\."+src[t.PRERELEASEIDENTIFIERLOOSE]+")*))",tok("BUILDIDENTIFIER"),src[t.BUILDIDENTIFIER]="[a-zA-Z0-9-]+",tok("BUILD"),src[t.BUILD]="(?:\\+("+src[t.BUILDIDENTIFIER]+"(?:\\."+src[t.BUILDIDENTIFIER]+")*))",tok("FULL"),tok("FULLPLAIN"),src[t.FULLPLAIN]="v?"+src[t.MAINVERSION]+src[t.PRERELEASE]+"?"+src[t.BUILD]+"?",src[t.FULL]="^"+src[t.FULLPLAIN]+"$",tok("LOOSEPLAIN"),src[t.LOOSEPLAIN]="[v=\\s]*"+src[t.MAINVERSIONLOOSE]+src[t.PRERELEASELOOSE]+"?"+src[t.BUILD]+"?",tok("LOOSE"),src[t.LOOSE]="^"+src[t.LOOSEPLAIN]+"$",tok("GTLT"),src[t.GTLT]="((?:<|>)?=?)",tok("XRANGEIDENTIFIERLOOSE"),src[t.XRANGEIDENTIFIERLOOSE]=src[t.NUMERICIDENTIFIERLOOSE]+"|x|X|\\*",tok("XRANGEIDENTIFIER"),src[t.XRANGEIDENTIFIER]=src[t.NUMERICIDENTIFIER]+"|x|X|\\*",tok("XRANGEPLAIN"),src[t.XRANGEPLAIN]="[v=\\s]*("+src[t.XRANGEIDENTIFIER]+")(?:\\.("+src[t.XRANGEIDENTIFIER]+")(?:\\.("+src[t.XRANGEIDENTIFIER]+")(?:"+src[t.PRERELEASE]+")?"+src[t.BUILD]+"?)?)?",tok("XRANGEPLAINLOOSE"),src[t.XRANGEPLAINLOOSE]="[v=\\s]*("+src[t.XRANGEIDENTIFIERLOOSE]+")(?:\\.("+src[t.XRANGEIDENTIFIERLOOSE]+")(?:\\.("+src[t.XRANGEIDENTIFIERLOOSE]+")(?:"+src[t.PRERELEASELOOSE]+")?"+src[t.BUILD]+"?)?)?",tok("XRANGE"),src[t.XRANGE]="^"+src[t.GTLT]+"\\s*"+src[t.XRANGEPLAIN]+"$",tok("XRANGELOOSE"),src[t.XRANGELOOSE]="^"+src[t.GTLT]+"\\s*"+src[t.XRANGEPLAINLOOSE]+"$",tok("COERCE"),src[t.COERCE]="(^|[^\\d])(\\d{1,16})(?:\\.(\\d{1,16}))?(?:\\.(\\d{1,16}))?(?:$|[^\\d])",tok("COERCERTL"),re[t.COERCERTL]=new RegExp(src[t.COERCE],"g"),safeRe[t.COERCERTL]=new RegExp(makeSafeRe(src[t.COERCE]),"g"),tok("LONETILDE"),src[t.LONETILDE]="(?:~>?)",tok("TILDETRIM"),src[t.TILDETRIM]="(\\s*)"+src[t.LONETILDE]+"\\s+",re[t.TILDETRIM]=new RegExp(src[t.TILDETRIM],"g"),safeRe[t.TILDETRIM]=new RegExp(makeSafeRe(src[t.TILDETRIM]),"g");tok("TILDE"),src[t.TILDE]="^"+src[t.LONETILDE]+src[t.XRANGEPLAIN]+"$",tok("TILDELOOSE"),src[t.TILDELOOSE]="^"+src[t.LONETILDE]+src[t.XRANGEPLAINLOOSE]+"$",tok("LONECARET"),src[t.LONECARET]="(?:\\^)",tok("CARETTRIM"),src[t.CARETTRIM]="(\\s*)"+src[t.LONECARET]+"\\s+",re[t.CARETTRIM]=new RegExp(src[t.CARETTRIM],"g"),safeRe[t.CARETTRIM]=new RegExp(makeSafeRe(src[t.CARETTRIM]),"g");tok("CARET"),src[t.CARET]="^"+src[t.LONECARET]+src[t.XRANGEPLAIN]+"$",tok("CARETLOOSE"),src[t.CARETLOOSE]="^"+src[t.LONECARET]+src[t.XRANGEPLAINLOOSE]+"$",tok("COMPARATORLOOSE"),src[t.COMPARATORLOOSE]="^"+src[t.GTLT]+"\\s*("+src[t.LOOSEPLAIN]+")$|^$",tok("COMPARATOR"),src[t.COMPARATOR]="^"+src[t.GTLT]+"\\s*("+src[t.FULLPLAIN]+")$|^$",tok("COMPARATORTRIM"),src[t.COMPARATORTRIM]="(\\s*)"+src[t.GTLT]+"\\s*("+src[t.LOOSEPLAIN]+"|"+src[t.XRANGEPLAIN]+")",re[t.COMPARATORTRIM]=new RegExp(src[t.COMPARATORTRIM],"g"),safeRe[t.COMPARATORTRIM]=new RegExp(makeSafeRe(src[t.COMPARATORTRIM]),"g");tok("HYPHENRANGE"),src[t.HYPHENRANGE]="^\\s*("+src[t.XRANGEPLAIN]+")\\s+-\\s+("+src[t.XRANGEPLAIN]+")\\s*$",tok("HYPHENRANGELOOSE"),src[t.HYPHENRANGELOOSE]="^\\s*("+src[t.XRANGEPLAINLOOSE]+")\\s+-\\s+("+src[t.XRANGEPLAINLOOSE]+")\\s*$",tok("STAR"),src[t.STAR]="(<|>)?=?\\s*\\*";for(var i=0;i<R;i++)debug(i,src[i]),re[i]||(re[i]=new RegExp(src[i]),safeRe[i]=new RegExp(makeSafeRe(src[i])));function parse(version,options){if(options&&"object"==typeof options||(options={loose:!!options,includePrerelease:!1}),version instanceof SemVer)return version;if("string"!=typeof version)return null;if(version.length>MAX_LENGTH)return null;if(!(options.loose?safeRe[t.LOOSE]:safeRe[t.FULL]).test(version))return null;try{return new SemVer(version,options)}catch(er){return null}}function SemVer(version,options){if(options&&"object"==typeof options||(options={loose:!!options,includePrerelease:!1}),version instanceof SemVer){if(version.loose===options.loose)return version;version=version.version}else if("string"!=typeof version)throw new TypeError("Invalid Version: "+version);if(version.length>MAX_LENGTH)throw new TypeError("version is longer than "+MAX_LENGTH+" characters");if(!(this instanceof SemVer))return new SemVer(version,options);debug("SemVer",version,options),this.options=options,this.loose=!!options.loose;var m=version.trim().match(options.loose?safeRe[t.LOOSE]:safeRe[t.FULL]);if(!m)throw new TypeError("Invalid Version: "+version);if(this.raw=version,this.major=+m[1],this.minor=+m[2],this.patch=+m[3],this.major>MAX_SAFE_INTEGER||this.major<0)throw new TypeError("Invalid major version");if(this.minor>MAX_SAFE_INTEGER||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>MAX_SAFE_INTEGER||this.patch<0)throw new TypeError("Invalid patch version");m[4]?this.prerelease=m[4].split(".").map((function(id){if(/^[0-9]+$/.test(id)){var num=+id;if(num>=0&&num<MAX_SAFE_INTEGER)return num}return id})):this.prerelease=[],this.build=m[5]?m[5].split("."):[],this.format()}exports.parse=parse,exports.valid=function(version,options){var v=parse(version,options);return v?v.version:null},exports.clean=function(version,options){var s=parse(version.trim().replace(/^[=v]+/,""),options);return s?s.version:null},exports.SemVer=SemVer,SemVer.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},SemVer.prototype.toString=function(){return this.version},SemVer.prototype.compare=function(other){return debug("SemVer.compare",this.version,this.options,other),other instanceof SemVer||(other=new SemVer(other,this.options)),this.compareMain(other)||this.comparePre(other)},SemVer.prototype.compareMain=function(other){return other instanceof SemVer||(other=new SemVer(other,this.options)),compareIdentifiers(this.major,other.major)||compareIdentifiers(this.minor,other.minor)||compareIdentifiers(this.patch,other.patch)},SemVer.prototype.comparePre=function(other){if(other instanceof SemVer||(other=new SemVer(other,this.options)),this.prerelease.length&&!other.prerelease.length)return-1;if(!this.prerelease.length&&other.prerelease.length)return 1;if(!this.prerelease.length&&!other.prerelease.length)return 0;var i=0;do{var a=this.prerelease[i],b=other.prerelease[i];if(debug("prerelease compare",i,a,b),void 0===a&&void 0===b)return 0;if(void 0===b)return 1;if(void 0===a)return-1;if(a!==b)return compareIdentifiers(a,b)}while(++i)},SemVer.prototype.compareBuild=function(other){other instanceof SemVer||(other=new SemVer(other,this.options));var i=0;do{var a=this.build[i],b=other.build[i];if(debug("prerelease compare",i,a,b),void 0===a&&void 0===b)return 0;if(void 0===b)return 1;if(void 0===a)return-1;if(a!==b)return compareIdentifiers(a,b)}while(++i)},SemVer.prototype.inc=function(release,identifier){switch(release){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",identifier);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",identifier);break;case"prepatch":this.prerelease.length=0,this.inc("patch",identifier),this.inc("pre",identifier);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",identifier),this.inc("pre",identifier);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var i=this.prerelease.length;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);-1===i&&this.prerelease.push(0)}identifier&&(this.prerelease[0]===identifier?isNaN(this.prerelease[1])&&(this.prerelease=[identifier,0]):this.prerelease=[identifier,0]);break;default:throw new Error("invalid increment argument: "+release)}return this.format(),this.raw=this.version,this},exports.inc=function(version,release,loose,identifier){"string"==typeof loose&&(identifier=loose,loose=void 0);try{return new SemVer(version,loose).inc(release,identifier).version}catch(er){return null}},exports.diff=function(version1,version2){if(eq(version1,version2))return null;var v1=parse(version1),v2=parse(version2),prefix="";if(v1.prerelease.length||v2.prerelease.length){prefix="pre";var defaultResult="prerelease"}for(var key in v1)if(("major"===key||"minor"===key||"patch"===key)&&v1[key]!==v2[key])return prefix+key;return defaultResult},exports.compareIdentifiers=compareIdentifiers;var numeric=/^[0-9]+$/;function compareIdentifiers(a,b){var anum=numeric.test(a),bnum=numeric.test(b);return anum&&bnum&&(a=+a,b=+b),a===b?0:anum&&!bnum?-1:bnum&&!anum?1:a<b?-1:1}function compare(a,b,loose){return new SemVer(a,loose).compare(new SemVer(b,loose))}function gt(a,b,loose){return compare(a,b,loose)>0}function lt(a,b,loose){return compare(a,b,loose)<0}function eq(a,b,loose){return 0===compare(a,b,loose)}function neq(a,b,loose){return 0!==compare(a,b,loose)}function gte(a,b,loose){return compare(a,b,loose)>=0}function lte(a,b,loose){return compare(a,b,loose)<=0}function cmp(a,op,b,loose){switch(op){case"===":return"object"==typeof a&&(a=a.version),"object"==typeof b&&(b=b.version),a===b;case"!==":return"object"==typeof a&&(a=a.version),"object"==typeof b&&(b=b.version),a!==b;case"":case"=":case"==":return eq(a,b,loose);case"!=":return neq(a,b,loose);case">":return gt(a,b,loose);case">=":return gte(a,b,loose);case"<":return lt(a,b,loose);case"<=":return lte(a,b,loose);default:throw new TypeError("Invalid operator: "+op)}}function Comparator(comp,options){if(options&&"object"==typeof options||(options={loose:!!options,includePrerelease:!1}),comp instanceof Comparator){if(comp.loose===!!options.loose)return comp;comp=comp.value}if(!(this instanceof Comparator))return new Comparator(comp,options);comp=comp.trim().split(/\s+/).join(" "),debug("comparator",comp,options),this.options=options,this.loose=!!options.loose,this.parse(comp),this.semver===ANY?this.value="":this.value=this.operator+this.semver.version,debug("comp",this)}exports.rcompareIdentifiers=function(a,b){return compareIdentifiers(b,a)},exports.major=function(a,loose){return new SemVer(a,loose).major},exports.minor=function(a,loose){return new SemVer(a,loose).minor},exports.patch=function(a,loose){return new SemVer(a,loose).patch},exports.compare=compare,exports.compareLoose=function(a,b){return compare(a,b,!0)},exports.compareBuild=function(a,b,loose){var versionA=new SemVer(a,loose),versionB=new SemVer(b,loose);return versionA.compare(versionB)||versionA.compareBuild(versionB)},exports.rcompare=function(a,b,loose){return compare(b,a,loose)},exports.sort=function(list,loose){return list.sort((function(a,b){return exports.compareBuild(a,b,loose)}))},exports.rsort=function(list,loose){return list.sort((function(a,b){return exports.compareBuild(b,a,loose)}))},exports.gt=gt,exports.lt=lt,exports.eq=eq,exports.neq=neq,exports.gte=gte,exports.lte=lte,exports.cmp=cmp,exports.Comparator=Comparator;var ANY={};function Range(range,options){if(options&&"object"==typeof options||(options={loose:!!options,includePrerelease:!1}),range instanceof Range)return range.loose===!!options.loose&&range.includePrerelease===!!options.includePrerelease?range:new Range(range.raw,options);if(range instanceof Comparator)return new Range(range.value,options);if(!(this instanceof Range))return new Range(range,options);if(this.options=options,this.loose=!!options.loose,this.includePrerelease=!!options.includePrerelease,this.raw=range.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map((function(range){return this.parseRange(range.trim())}),this).filter((function(c){return c.length})),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);this.format()}function isSatisfiable(comparators,options){for(var result=!0,remainingComparators=comparators.slice(),testComparator=remainingComparators.pop();result&&remainingComparators.length;)result=remainingComparators.every((function(otherComparator){return testComparator.intersects(otherComparator,options)})),testComparator=remainingComparators.pop();return result}function isX(id){return!id||"x"===id.toLowerCase()||"*"===id}function hyphenReplace($0,from,fM,fm,fp,fpr,fb,to,tM,tm,tp,tpr,tb){return((from=isX(fM)?"":isX(fm)?">="+fM+".0.0":isX(fp)?">="+fM+"."+fm+".0":">="+from)+" "+(to=isX(tM)?"":isX(tm)?"<"+(+tM+1)+".0.0":isX(tp)?"<"+tM+"."+(+tm+1)+".0":tpr?"<="+tM+"."+tm+"."+tp+"-"+tpr:"<="+to)).trim()}function testSet(set,version,options){for(var i=0;i<set.length;i++)if(!set[i].test(version))return!1;if(version.prerelease.length&&!options.includePrerelease){for(i=0;i<set.length;i++)if(debug(set[i].semver),set[i].semver!==ANY&&set[i].semver.prerelease.length>0){var allowed=set[i].semver;if(allowed.major===version.major&&allowed.minor===version.minor&&allowed.patch===version.patch)return!0}return!1}return!0}function satisfies(version,range,options){try{range=new Range(range,options)}catch(er){return!1}return range.test(version)}function outside(version,range,hilo,options){var gtfn,ltefn,ltfn,comp,ecomp;switch(version=new SemVer(version,options),range=new Range(range,options),hilo){case">":gtfn=gt,ltefn=lte,ltfn=lt,comp=">",ecomp=">=";break;case"<":gtfn=lt,ltefn=gte,ltfn=gt,comp="<",ecomp="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(satisfies(version,range,options))return!1;for(var i=0;i<range.set.length;++i){var comparators=range.set[i],high=null,low=null;if(comparators.forEach((function(comparator){comparator.semver===ANY&&(comparator=new Comparator(">=0.0.0")),high=high||comparator,low=low||comparator,gtfn(comparator.semver,high.semver,options)?high=comparator:ltfn(comparator.semver,low.semver,options)&&(low=comparator)})),high.operator===comp||high.operator===ecomp)return!1;if((!low.operator||low.operator===comp)&&ltefn(version,low.semver))return!1;if(low.operator===ecomp&&ltfn(version,low.semver))return!1}return!0}Comparator.prototype.parse=function(comp){var r=this.options.loose?safeRe[t.COMPARATORLOOSE]:safeRe[t.COMPARATOR],m=comp.match(r);if(!m)throw new TypeError("Invalid comparator: "+comp);this.operator=void 0!==m[1]?m[1]:"","="===this.operator&&(this.operator=""),m[2]?this.semver=new SemVer(m[2],this.options.loose):this.semver=ANY},Comparator.prototype.toString=function(){return this.value},Comparator.prototype.test=function(version){if(debug("Comparator.test",version,this.options.loose),this.semver===ANY||version===ANY)return!0;if("string"==typeof version)try{version=new SemVer(version,this.options)}catch(er){return!1}return cmp(version,this.operator,this.semver,this.options)},Comparator.prototype.intersects=function(comp,options){if(!(comp instanceof Comparator))throw new TypeError("a Comparator is required");var rangeTmp;if(options&&"object"==typeof options||(options={loose:!!options,includePrerelease:!1}),""===this.operator)return""===this.value||(rangeTmp=new Range(comp.value,options),satisfies(this.value,rangeTmp,options));if(""===comp.operator)return""===comp.value||(rangeTmp=new Range(this.value,options),satisfies(comp.semver,rangeTmp,options));var sameDirectionIncreasing=!(">="!==this.operator&&">"!==this.operator||">="!==comp.operator&&">"!==comp.operator),sameDirectionDecreasing=!("<="!==this.operator&&"<"!==this.operator||"<="!==comp.operator&&"<"!==comp.operator),sameSemVer=this.semver.version===comp.semver.version,differentDirectionsInclusive=!(">="!==this.operator&&"<="!==this.operator||">="!==comp.operator&&"<="!==comp.operator),oppositeDirectionsLessThan=cmp(this.semver,"<",comp.semver,options)&&(">="===this.operator||">"===this.operator)&&("<="===comp.operator||"<"===comp.operator),oppositeDirectionsGreaterThan=cmp(this.semver,">",comp.semver,options)&&("<="===this.operator||"<"===this.operator)&&(">="===comp.operator||">"===comp.operator);return sameDirectionIncreasing||sameDirectionDecreasing||sameSemVer&&differentDirectionsInclusive||oppositeDirectionsLessThan||oppositeDirectionsGreaterThan},exports.Range=Range,Range.prototype.format=function(){return this.range=this.set.map((function(comps){return comps.join(" ").trim()})).join("||").trim(),this.range},Range.prototype.toString=function(){return this.range},Range.prototype.parseRange=function(range){var loose=this.options.loose,hr=loose?safeRe[t.HYPHENRANGELOOSE]:safeRe[t.HYPHENRANGE];range=range.replace(hr,hyphenReplace),debug("hyphen replace",range),range=range.replace(safeRe[t.COMPARATORTRIM],"$1$2$3"),debug("comparator trim",range,safeRe[t.COMPARATORTRIM]),range=(range=(range=range.replace(safeRe[t.TILDETRIM],"$1~")).replace(safeRe[t.CARETTRIM],"$1^")).split(/\s+/).join(" ");var compRe=loose?safeRe[t.COMPARATORLOOSE]:safeRe[t.COMPARATOR],set=range.split(" ").map((function(comp){return function(comp,options){return debug("comp",comp,options),comp=function(comp,options){return comp.trim().split(/\s+/).map((function(comp){return function(comp,options){debug("caret",comp,options);var r=options.loose?safeRe[t.CARETLOOSE]:safeRe[t.CARET];return comp.replace(r,(function(_,M,m,p,pr){var ret;return debug("caret",comp,_,M,m,p,pr),isX(M)?ret="":isX(m)?ret=">="+M+".0.0 <"+(+M+1)+".0.0":isX(p)?ret="0"===M?">="+M+"."+m+".0 <"+M+"."+(+m+1)+".0":">="+M+"."+m+".0 <"+(+M+1)+".0.0":pr?(debug("replaceCaret pr",pr),ret="0"===M?"0"===m?">="+M+"."+m+"."+p+"-"+pr+" <"+M+"."+m+"."+(+p+1):">="+M+"."+m+"."+p+"-"+pr+" <"+M+"."+(+m+1)+".0":">="+M+"."+m+"."+p+"-"+pr+" <"+(+M+1)+".0.0"):(debug("no pr"),ret="0"===M?"0"===m?">="+M+"."+m+"."+p+" <"+M+"."+m+"."+(+p+1):">="+M+"."+m+"."+p+" <"+M+"."+(+m+1)+".0":">="+M+"."+m+"."+p+" <"+(+M+1)+".0.0"),debug("caret return",ret),ret}))}(comp,options)})).join(" ")}(comp,options),debug("caret",comp),comp=function(comp,options){return comp.trim().split(/\s+/).map((function(comp){return function(comp,options){var r=options.loose?safeRe[t.TILDELOOSE]:safeRe[t.TILDE];return comp.replace(r,(function(_,M,m,p,pr){var ret;return debug("tilde",comp,_,M,m,p,pr),isX(M)?ret="":isX(m)?ret=">="+M+".0.0 <"+(+M+1)+".0.0":isX(p)?ret=">="+M+"."+m+".0 <"+M+"."+(+m+1)+".0":pr?(debug("replaceTilde pr",pr),ret=">="+M+"."+m+"."+p+"-"+pr+" <"+M+"."+(+m+1)+".0"):ret=">="+M+"."+m+"."+p+" <"+M+"."+(+m+1)+".0",debug("tilde return",ret),ret}))}(comp,options)})).join(" ")}(comp,options),debug("tildes",comp),comp=function(comp,options){return debug("replaceXRanges",comp,options),comp.split(/\s+/).map((function(comp){return function(comp,options){comp=comp.trim();var r=options.loose?safeRe[t.XRANGELOOSE]:safeRe[t.XRANGE];return comp.replace(r,(function(ret,gtlt,M,m,p,pr){debug("xRange",comp,ret,gtlt,M,m,p,pr);var xM=isX(M),xm=xM||isX(m),xp=xm||isX(p),anyX=xp;return"="===gtlt&&anyX&&(gtlt=""),pr=options.includePrerelease?"-0":"",xM?ret=">"===gtlt||"<"===gtlt?"<0.0.0-0":"*":gtlt&&anyX?(xm&&(m=0),p=0,">"===gtlt?(gtlt=">=",xm?(M=+M+1,m=0,p=0):(m=+m+1,p=0)):"<="===gtlt&&(gtlt="<",xm?M=+M+1:m=+m+1),ret=gtlt+M+"."+m+"."+p+pr):xm?ret=">="+M+".0.0"+pr+" <"+(+M+1)+".0.0"+pr:xp&&(ret=">="+M+"."+m+".0"+pr+" <"+M+"."+(+m+1)+".0"+pr),debug("xRange return",ret),ret}))}(comp,options)})).join(" ")}(comp,options),debug("xrange",comp),comp=function(comp,options){return debug("replaceStars",comp,options),comp.trim().replace(safeRe[t.STAR],"")}(comp,options),debug("stars",comp),comp}(comp,this.options)}),this).join(" ").split(/\s+/);return this.options.loose&&(set=set.filter((function(comp){return!!comp.match(compRe)}))),set=set.map((function(comp){return new Comparator(comp,this.options)}),this)},Range.prototype.intersects=function(range,options){if(!(range instanceof Range))throw new TypeError("a Range is required");return this.set.some((function(thisComparators){return isSatisfiable(thisComparators,options)&&range.set.some((function(rangeComparators){return isSatisfiable(rangeComparators,options)&&thisComparators.every((function(thisComparator){return rangeComparators.every((function(rangeComparator){return thisComparator.intersects(rangeComparator,options)}))}))}))}))},exports.toComparators=function(range,options){return new Range(range,options).set.map((function(comp){return comp.map((function(c){return c.value})).join(" ").trim().split(" ")}))},Range.prototype.test=function(version){if(!version)return!1;if("string"==typeof version)try{version=new SemVer(version,this.options)}catch(er){return!1}for(var i=0;i<this.set.length;i++)if(testSet(this.set[i],version,this.options))return!0;return!1},exports.satisfies=satisfies,exports.maxSatisfying=function(versions,range,options){var max=null,maxSV=null;try{var rangeObj=new Range(range,options)}catch(er){return null}return versions.forEach((function(v){rangeObj.test(v)&&(max&&-1!==maxSV.compare(v)||(maxSV=new SemVer(max=v,options)))})),max},exports.minSatisfying=function(versions,range,options){var min=null,minSV=null;try{var rangeObj=new Range(range,options)}catch(er){return null}return versions.forEach((function(v){rangeObj.test(v)&&(min&&1!==minSV.compare(v)||(minSV=new SemVer(min=v,options)))})),min},exports.minVersion=function(range,loose){range=new Range(range,loose);var minver=new SemVer("0.0.0");if(range.test(minver))return minver;if(minver=new SemVer("0.0.0-0"),range.test(minver))return minver;minver=null;for(var i=0;i<range.set.length;++i){range.set[i].forEach((function(comparator){var compver=new SemVer(comparator.semver.version);switch(comparator.operator){case">":0===compver.prerelease.length?compver.patch++:compver.prerelease.push(0),compver.raw=compver.format();case"":case">=":minver&&!gt(minver,compver)||(minver=compver);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+comparator.operator)}}))}if(minver&&range.test(minver))return minver;return null},exports.validRange=function(range,options){try{return new Range(range,options).range||"*"}catch(er){return null}},exports.ltr=function(version,range,options){return outside(version,range,"<",options)},exports.gtr=function(version,range,options){return outside(version,range,">",options)},exports.outside=outside,exports.prerelease=function(version,options){var parsed=parse(version,options);return parsed&&parsed.prerelease.length?parsed.prerelease:null},exports.intersects=function(r1,r2,options){return r1=new Range(r1,options),r2=new Range(r2,options),r1.intersects(r2)},exports.coerce=function(version,options){if(version instanceof SemVer)return version;"number"==typeof version&&(version=String(version));if("string"!=typeof version)return null;var match=null;if((options=options||{}).rtl){for(var next;(next=safeRe[t.COERCERTL].exec(version))&&(!match||match.index+match[0].length!==version.length);)match&&next.index+next[0].length===match.index+match[0].length||(match=next),safeRe[t.COERCERTL].lastIndex=next.index+next[1].length+next[2].length;safeRe[t.COERCERTL].lastIndex=-1}else match=version.match(safeRe[t.COERCE]);if(null===match)return null;return parse(match[2]+"."+(match[3]||"0")+"."+(match[4]||"0"),options)}},"./node_modules/.pnpm/supports-color@7.2.0/node_modules/supports-color/index.js":(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";const os=__webpack_require__("os"),tty=__webpack_require__("tty"),hasFlag=__webpack_require__("./node_modules/.pnpm/has-flag@4.0.0/node_modules/has-flag/index.js"),{env}=process;let forceColor;function translateLevel(level){return 0!==level&&{level,hasBasic:!0,has256:level>=2,has16m:level>=3}}function supportsColor(haveStream,streamIsTTY){if(0===forceColor)return 0;if(hasFlag("color=16m")||hasFlag("color=full")||hasFlag("color=truecolor"))return 3;if(hasFlag("color=256"))return 2;if(haveStream&&!streamIsTTY&&void 0===forceColor)return 0;const min=forceColor||0;if("dumb"===env.TERM)return min;if("win32"===process.platform){const osRelease=os.release().split(".");return Number(osRelease[0])>=10&&Number(osRelease[2])>=10586?Number(osRelease[2])>=14931?3:2:1}if("CI"in env)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((sign=>sign in env))||"codeship"===env.CI_NAME?1:min;if("TEAMCITY_VERSION"in env)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(env.TEAMCITY_VERSION)?1:0;if("truecolor"===env.COLORTERM)return 3;if("TERM_PROGRAM"in env){const version=parseInt((env.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(env.TERM_PROGRAM){case"iTerm.app":return version>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(env.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)||"COLORTERM"in env?1:min}hasFlag("no-color")||hasFlag("no-colors")||hasFlag("color=false")||hasFlag("color=never")?forceColor=0:(hasFlag("color")||hasFlag("colors")||hasFlag("color=true")||hasFlag("color=always"))&&(forceColor=1),"FORCE_COLOR"in env&&(forceColor="true"===env.FORCE_COLOR?1:"false"===env.FORCE_COLOR?0:0===env.FORCE_COLOR.length?1:Math.min(parseInt(env.FORCE_COLOR,10),3)),module.exports={supportsColor:function(stream){return translateLevel(supportsColor(stream,stream&&stream.isTTY))},stdout:translateLevel(supportsColor(!0,tty.isatty(1))),stderr:translateLevel(supportsColor(!0,tty.isatty(2)))}},assert:module=>{"use strict";module.exports=require("assert")},fs:module=>{"use strict";module.exports=require("fs")},module:module=>{"use strict";module.exports=require("module")},os:module=>{"use strict";module.exports=require("os")},path:module=>{"use strict";module.exports=require("path")},process:module=>{"use strict";module.exports=require("process")},tty:module=>{"use strict";module.exports=require("tty")},url:module=>{"use strict";module.exports=require("url")},util:module=>{"use strict";module.exports=require("util")},v8:module=>{"use strict";module.exports=require("v8")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.assertSimpleType=assertSimpleType,exports.makeStrongCache=makeStrongCache,exports.makeStrongCacheSync=function(handler){return synchronize(makeStrongCache(handler))},exports.makeWeakCache=makeWeakCache,exports.makeWeakCacheSync=function(handler){return synchronize(makeWeakCache(handler))};var _async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js"),_util=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/util.js");const synchronize=gen=>_gensync()(gen).sync;function*genTrue(){return!0}function makeWeakCache(handler){return makeCachedFunction(WeakMap,handler)}function makeStrongCache(handler){return makeCachedFunction(Map,handler)}function makeCachedFunction(CallCache,handler){const callCacheSync=new CallCache,callCacheAsync=new CallCache,futureCache=new CallCache;return function*(arg,data){const asyncContext=yield*(0,_async.isAsync)(),callCache=asyncContext?callCacheAsync:callCacheSync,cached=yield*function*(asyncContext,callCache,futureCache,arg,data){const cached=yield*getCachedValue(callCache,arg,data);if(cached.valid)return cached;if(asyncContext){const cached=yield*getCachedValue(futureCache,arg,data);if(cached.valid){return{valid:!0,value:yield*(0,_async.waitFor)(cached.value.promise)}}}return{valid:!1,value:null}}(asyncContext,callCache,futureCache,arg,data);if(cached.valid)return cached.value;const cache=new CacheConfigurator(data),handlerResult=handler(arg,cache);let finishLock,value;return value=(0,_util.isIterableIterator)(handlerResult)?yield*(0,_async.onFirstPause)(handlerResult,(()=>{finishLock=function(config,futureCache,arg){const finishLock=new Lock;return updateFunctionCache(futureCache,config,arg,finishLock),finishLock}(cache,futureCache,arg)})):handlerResult,updateFunctionCache(callCache,cache,arg,value),finishLock&&(futureCache.delete(arg),finishLock.release(value)),value}}function*getCachedValue(cache,arg,data){const cachedValue=cache.get(arg);if(cachedValue)for(const{value,valid}of cachedValue)if(yield*valid(data))return{valid:!0,value};return{valid:!1,value:null}}function updateFunctionCache(cache,config,arg,value){config.configured()||config.forever();let cachedValue=cache.get(arg);switch(config.deactivate(),config.mode()){case"forever":cachedValue=[{value,valid:genTrue}],cache.set(arg,cachedValue);break;case"invalidate":cachedValue=[{value,valid:config.validator()}],cache.set(arg,cachedValue);break;case"valid":cachedValue?cachedValue.push({value,valid:config.validator()}):(cachedValue=[{value,valid:config.validator()}],cache.set(arg,cachedValue))}}class CacheConfigurator{constructor(data){this._active=!0,this._never=!1,this._forever=!1,this._invalidate=!1,this._configured=!1,this._pairs=[],this._data=void 0,this._data=data}simple(){return function(cache){function cacheFn(val){if("boolean"!=typeof val)return cache.using((()=>assertSimpleType(val())));val?cache.forever():cache.never()}return cacheFn.forever=()=>cache.forever(),cacheFn.never=()=>cache.never(),cacheFn.using=cb=>cache.using((()=>assertSimpleType(cb()))),cacheFn.invalidate=cb=>cache.invalidate((()=>assertSimpleType(cb()))),cacheFn}(this)}mode(){return this._never?"never":this._forever?"forever":this._invalidate?"invalidate":"valid"}forever(){if(!this._active)throw new Error("Cannot change caching after evaluation has completed.");if(this._never)throw new Error("Caching has already been configured with .never()");this._forever=!0,this._configured=!0}never(){if(!this._active)throw new Error("Cannot change caching after evaluation has completed.");if(this._forever)throw new Error("Caching has already been configured with .forever()");this._never=!0,this._configured=!0}using(handler){if(!this._active)throw new Error("Cannot change caching after evaluation has completed.");if(this._never||this._forever)throw new Error("Caching has already been configured with .never or .forever()");this._configured=!0;const key=handler(this._data),fn=(0,_async.maybeAsync)(handler,"You appear to be using an async cache handler, but Babel has been called synchronously");return(0,_async.isThenable)(key)?key.then((key=>(this._pairs.push([key,fn]),key))):(this._pairs.push([key,fn]),key)}invalidate(handler){return this._invalidate=!0,this.using(handler)}validator(){const pairs=this._pairs;return function*(data){for(const[key,fn]of pairs)if(key!==(yield*fn(data)))return!1;return!0}}deactivate(){this._active=!1}configured(){return this._configured}}function assertSimpleType(value){if((0,_async.isThenable)(value))throw new Error("You appear to be using an async cache handler, which your current version of Babel does not support. We may add support for this in the future, but if you're on the most recent version of @babel/core and still seeing this error, then you'll need to synchronously handle your caching logic.");if(null!=value&&"string"!=typeof value&&"boolean"!=typeof value&&"number"!=typeof value)throw new Error("Cache keys must be either string, boolean, number, null, or undefined.");return value}class Lock{constructor(){this.released=!1,this.promise=void 0,this._resolve=void 0,this.promise=new Promise((resolve=>{this._resolve=resolve}))}release(value){this.released=!0,this._resolve(value)}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-chain.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}function _debug(){const data=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js");return _debug=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.buildPresetChain=function*(arg,context){const chain=yield*buildPresetChainWalker(arg,context);return chain?{plugins:dedupDescriptors(chain.plugins),presets:dedupDescriptors(chain.presets),options:chain.options.map((o=>normalizeOptions(o))),files:new Set}:null},exports.buildPresetChainWalker=void 0,exports.buildRootChain=function*(opts,context){let configReport,babelRcReport;const programmaticLogger=new _printer.ConfigPrinter,programmaticChain=yield*loadProgrammaticChain({options:opts,dirname:context.cwd},context,void 0,programmaticLogger);if(!programmaticChain)return null;const programmaticReport=yield*programmaticLogger.output();let configFile;"string"==typeof opts.configFile?configFile=yield*(0,_index.loadConfig)(opts.configFile,context.cwd,context.envName,context.caller):!1!==opts.configFile&&(configFile=yield*(0,_index.findRootConfig)(context.root,context.envName,context.caller));let{babelrc,babelrcRoots}=opts,babelrcRootsDirectory=context.cwd;const configFileChain=emptyChain(),configFileLogger=new _printer.ConfigPrinter;if(configFile){const validatedFile=validateConfigFile(configFile),result=yield*loadFileChain(validatedFile,context,void 0,configFileLogger);if(!result)return null;configReport=yield*configFileLogger.output(),void 0===babelrc&&(babelrc=validatedFile.options.babelrc),void 0===babelrcRoots&&(babelrcRootsDirectory=validatedFile.dirname,babelrcRoots=validatedFile.options.babelrcRoots),mergeChain(configFileChain,result)}let ignoreFile,babelrcFile,isIgnored=!1;const fileChain=emptyChain();if((!0===babelrc||void 0===babelrc)&&"string"==typeof context.filename){const pkgData=yield*(0,_index.findPackageData)(context.filename);if(pkgData&&function(context,pkgData,babelrcRoots,babelrcRootsDirectory){if("boolean"==typeof babelrcRoots)return babelrcRoots;const absoluteRoot=context.root;if(void 0===babelrcRoots)return pkgData.directories.includes(absoluteRoot);let babelrcPatterns=babelrcRoots;Array.isArray(babelrcPatterns)||(babelrcPatterns=[babelrcPatterns]);if(babelrcPatterns=babelrcPatterns.map((pat=>"string"==typeof pat?_path().resolve(babelrcRootsDirectory,pat):pat)),1===babelrcPatterns.length&&babelrcPatterns[0]===absoluteRoot)return pkgData.directories.includes(absoluteRoot);return babelrcPatterns.some((pat=>("string"==typeof pat&&(pat=(0,_patternToRegex.default)(pat,babelrcRootsDirectory)),pkgData.directories.some((directory=>matchPattern(pat,babelrcRootsDirectory,directory,context))))))}(context,pkgData,babelrcRoots,babelrcRootsDirectory)){if(({ignore:ignoreFile,config:babelrcFile}=yield*(0,_index.findRelativeConfig)(pkgData,context.envName,context.caller)),ignoreFile&&fileChain.files.add(ignoreFile.filepath),ignoreFile&&shouldIgnore(context,ignoreFile.ignore,null,ignoreFile.dirname)&&(isIgnored=!0),babelrcFile&&!isIgnored){const validatedFile=validateBabelrcFile(babelrcFile),babelrcLogger=new _printer.ConfigPrinter,result=yield*loadFileChain(validatedFile,context,void 0,babelrcLogger);result?(babelRcReport=yield*babelrcLogger.output(),mergeChain(fileChain,result)):isIgnored=!0}babelrcFile&&isIgnored&&fileChain.files.add(babelrcFile.filepath)}}context.showConfig&&console.log(`Babel configs on "${context.filename}" (ascending priority):\n`+[configReport,babelRcReport,programmaticReport].filter((x=>!!x)).join("\n\n")+"\n-----End Babel configs-----");const chain=mergeChain(mergeChain(mergeChain(emptyChain(),configFileChain),fileChain),programmaticChain);return{plugins:isIgnored?[]:dedupDescriptors(chain.plugins),presets:isIgnored?[]:dedupDescriptors(chain.presets),options:isIgnored?[]:chain.options.map((o=>normalizeOptions(o))),fileHandling:isIgnored?"ignored":"transpile",ignore:ignoreFile||void 0,babelrc:babelrcFile||void 0,config:configFile||void 0,files:chain.files}};var _options=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/options.js"),_patternToRegex=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/pattern-to-regex.js"),_printer=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/printer.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js"),_index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/index.js"),_caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js"),_configDescriptors=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-descriptors.js");const debug=_debug()("babel:config:config-chain");const buildPresetChainWalker=exports.buildPresetChainWalker=makeChainWalker({root:preset=>loadPresetDescriptors(preset),env:(preset,envName)=>loadPresetEnvDescriptors(preset)(envName),overrides:(preset,index)=>loadPresetOverridesDescriptors(preset)(index),overridesEnv:(preset,index,envName)=>loadPresetOverridesEnvDescriptors(preset)(index)(envName),createLogger:()=>()=>{}}),loadPresetDescriptors=(0,_caching.makeWeakCacheSync)((preset=>buildRootDescriptors(preset,preset.alias,_configDescriptors.createUncachedDescriptors))),loadPresetEnvDescriptors=(0,_caching.makeWeakCacheSync)((preset=>(0,_caching.makeStrongCacheSync)((envName=>buildEnvDescriptors(preset,preset.alias,_configDescriptors.createUncachedDescriptors,envName))))),loadPresetOverridesDescriptors=(0,_caching.makeWeakCacheSync)((preset=>(0,_caching.makeStrongCacheSync)((index=>buildOverrideDescriptors(preset,preset.alias,_configDescriptors.createUncachedDescriptors,index))))),loadPresetOverridesEnvDescriptors=(0,_caching.makeWeakCacheSync)((preset=>(0,_caching.makeStrongCacheSync)((index=>(0,_caching.makeStrongCacheSync)((envName=>buildOverrideEnvDescriptors(preset,preset.alias,_configDescriptors.createUncachedDescriptors,index,envName)))))));const validateConfigFile=(0,_caching.makeWeakCacheSync)((file=>({filepath:file.filepath,dirname:file.dirname,options:(0,_options.validate)("configfile",file.options,file.filepath)}))),validateBabelrcFile=(0,_caching.makeWeakCacheSync)((file=>({filepath:file.filepath,dirname:file.dirname,options:(0,_options.validate)("babelrcfile",file.options,file.filepath)}))),validateExtendFile=(0,_caching.makeWeakCacheSync)((file=>({filepath:file.filepath,dirname:file.dirname,options:(0,_options.validate)("extendsfile",file.options,file.filepath)}))),loadProgrammaticChain=makeChainWalker({root:input=>buildRootDescriptors(input,"base",_configDescriptors.createCachedDescriptors),env:(input,envName)=>buildEnvDescriptors(input,"base",_configDescriptors.createCachedDescriptors,envName),overrides:(input,index)=>buildOverrideDescriptors(input,"base",_configDescriptors.createCachedDescriptors,index),overridesEnv:(input,index,envName)=>buildOverrideEnvDescriptors(input,"base",_configDescriptors.createCachedDescriptors,index,envName),createLogger:(input,context,baseLogger)=>function(_,context,baseLogger){var _context$caller;if(!baseLogger)return()=>{};return baseLogger.configure(context.showConfig,_printer.ChainFormatter.Programmatic,{callerName:null==(_context$caller=context.caller)?void 0:_context$caller.name})}(0,context,baseLogger)}),loadFileChainWalker=makeChainWalker({root:file=>loadFileDescriptors(file),env:(file,envName)=>loadFileEnvDescriptors(file)(envName),overrides:(file,index)=>loadFileOverridesDescriptors(file)(index),overridesEnv:(file,index,envName)=>loadFileOverridesEnvDescriptors(file)(index)(envName),createLogger:(file,context,baseLogger)=>function(filepath,context,baseLogger){if(!baseLogger)return()=>{};return baseLogger.configure(context.showConfig,_printer.ChainFormatter.Config,{filepath})}(file.filepath,context,baseLogger)});function*loadFileChain(input,context,files,baseLogger){const chain=yield*loadFileChainWalker(input,context,files,baseLogger);return null==chain||chain.files.add(input.filepath),chain}const loadFileDescriptors=(0,_caching.makeWeakCacheSync)((file=>buildRootDescriptors(file,file.filepath,_configDescriptors.createUncachedDescriptors))),loadFileEnvDescriptors=(0,_caching.makeWeakCacheSync)((file=>(0,_caching.makeStrongCacheSync)((envName=>buildEnvDescriptors(file,file.filepath,_configDescriptors.createUncachedDescriptors,envName))))),loadFileOverridesDescriptors=(0,_caching.makeWeakCacheSync)((file=>(0,_caching.makeStrongCacheSync)((index=>buildOverrideDescriptors(file,file.filepath,_configDescriptors.createUncachedDescriptors,index))))),loadFileOverridesEnvDescriptors=(0,_caching.makeWeakCacheSync)((file=>(0,_caching.makeStrongCacheSync)((index=>(0,_caching.makeStrongCacheSync)((envName=>buildOverrideEnvDescriptors(file,file.filepath,_configDescriptors.createUncachedDescriptors,index,envName)))))));function buildRootDescriptors({dirname,options},alias,descriptors){return descriptors(dirname,options,alias)}function buildEnvDescriptors({dirname,options},alias,descriptors,envName){var _options$env;const opts=null==(_options$env=options.env)?void 0:_options$env[envName];return opts?descriptors(dirname,opts,`${alias}.env["${envName}"]`):null}function buildOverrideDescriptors({dirname,options},alias,descriptors,index){var _options$overrides;const opts=null==(_options$overrides=options.overrides)?void 0:_options$overrides[index];if(!opts)throw new Error("Assertion failure - missing override");return descriptors(dirname,opts,`${alias}.overrides[${index}]`)}function buildOverrideEnvDescriptors({dirname,options},alias,descriptors,index,envName){var _options$overrides2,_override$env;const override=null==(_options$overrides2=options.overrides)?void 0:_options$overrides2[index];if(!override)throw new Error("Assertion failure - missing override");const opts=null==(_override$env=override.env)?void 0:_override$env[envName];return opts?descriptors(dirname,opts,`${alias}.overrides[${index}].env["${envName}"]`):null}function makeChainWalker({root,env,overrides,overridesEnv,createLogger}){return function*(input,context,files=new Set,baseLogger){const{dirname}=input,flattenedConfigs=[],rootOpts=root(input);if(configIsApplicable(rootOpts,dirname,context,input.filepath)){flattenedConfigs.push({config:rootOpts,envName:void 0,index:void 0});const envOpts=env(input,context.envName);envOpts&&configIsApplicable(envOpts,dirname,context,input.filepath)&&flattenedConfigs.push({config:envOpts,envName:context.envName,index:void 0}),(rootOpts.options.overrides||[]).forEach(((_,index)=>{const overrideOps=overrides(input,index);if(configIsApplicable(overrideOps,dirname,context,input.filepath)){flattenedConfigs.push({config:overrideOps,index,envName:void 0});const overrideEnvOpts=overridesEnv(input,index,context.envName);overrideEnvOpts&&configIsApplicable(overrideEnvOpts,dirname,context,input.filepath)&&flattenedConfigs.push({config:overrideEnvOpts,index,envName:context.envName})}}))}if(flattenedConfigs.some((({config:{options:{ignore,only}}})=>shouldIgnore(context,ignore,only,dirname))))return null;const chain=emptyChain(),logger=createLogger(input,context,baseLogger);for(const{config,index,envName}of flattenedConfigs){if(!(yield*mergeExtendsChain(chain,config.options,dirname,context,files,baseLogger)))return null;logger(config,index,envName),yield*mergeChainOpts(chain,config)}return chain}}function*mergeExtendsChain(chain,opts,dirname,context,files,baseLogger){if(void 0===opts.extends)return!0;const file=yield*(0,_index.loadConfig)(opts.extends,dirname,context.envName,context.caller);if(files.has(file))throw new Error(`Configuration cycle detected loading ${file.filepath}.\nFile already loaded following the config chain:\n`+Array.from(files,(file=>` - ${file.filepath}`)).join("\n"));files.add(file);const fileChain=yield*loadFileChain(validateExtendFile(file),context,files,baseLogger);return files.delete(file),!!fileChain&&(mergeChain(chain,fileChain),!0)}function mergeChain(target,source){target.options.push(...source.options),target.plugins.push(...source.plugins),target.presets.push(...source.presets);for(const file of source.files)target.files.add(file);return target}function*mergeChainOpts(target,{options,plugins,presets}){return target.options.push(options),target.plugins.push(...yield*plugins()),target.presets.push(...yield*presets()),target}function emptyChain(){return{options:[],presets:[],plugins:[],files:new Set}}function normalizeOptions(opts){const options=Object.assign({},opts);return delete options.extends,delete options.env,delete options.overrides,delete options.plugins,delete options.presets,delete options.passPerPreset,delete options.ignore,delete options.only,delete options.test,delete options.include,delete options.exclude,hasOwnProperty.call(options,"sourceMap")&&(options.sourceMaps=options.sourceMap,delete options.sourceMap),options}function dedupDescriptors(items){const map=new Map,descriptors=[];for(const item of items)if("function"==typeof item.value){const fnKey=item.value;let nameMap=map.get(fnKey);nameMap||(nameMap=new Map,map.set(fnKey,nameMap));let desc=nameMap.get(item.name);desc?desc.value=item:(desc={value:item},descriptors.push(desc),item.ownPass||nameMap.set(item.name,desc))}else descriptors.push({value:item});return descriptors.reduce(((acc,desc)=>(acc.push(desc.value),acc)),[])}function configIsApplicable({options},dirname,context,configName){return(void 0===options.test||configFieldIsApplicable(context,options.test,dirname,configName))&&(void 0===options.include||configFieldIsApplicable(context,options.include,dirname,configName))&&(void 0===options.exclude||!configFieldIsApplicable(context,options.exclude,dirname,configName))}function configFieldIsApplicable(context,test,dirname,configName){return matchesPatterns(context,Array.isArray(test)?test:[test],dirname,configName)}function ignoreListReplacer(_key,value){return value instanceof RegExp?String(value):value}function shouldIgnore(context,ignore,only,dirname){if(ignore&&matchesPatterns(context,ignore,dirname)){var _context$filename;const message=`No config is applied to "${null!=(_context$filename=context.filename)?_context$filename:"(unknown)"}" because it matches one of \`ignore: ${JSON.stringify(ignore,ignoreListReplacer)}\` from "${dirname}"`;return debug(message),context.showConfig&&console.log(message),!0}if(only&&!matchesPatterns(context,only,dirname)){var _context$filename2;const message=`No config is applied to "${null!=(_context$filename2=context.filename)?_context$filename2:"(unknown)"}" because it fails to match one of \`only: ${JSON.stringify(only,ignoreListReplacer)}\` from "${dirname}"`;return debug(message),context.showConfig&&console.log(message),!0}return!1}function matchesPatterns(context,patterns,dirname,configName){return patterns.some((pattern=>matchPattern(pattern,dirname,context.filename,context,configName)))}function matchPattern(pattern,dirname,pathToTest,context,configName){if("function"==typeof pattern)return!!(0,_rewriteStackTrace.endHiddenCallStack)(pattern)(pathToTest,{dirname,envName:context.envName,caller:context.caller});if("string"!=typeof pathToTest)throw new _configError.default("Configuration contains string/RegExp pattern, but no filename was passed to Babel",configName);return"string"==typeof pattern&&(pattern=(0,_patternToRegex.default)(pattern,dirname)),pattern.test(pathToTest)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-descriptors.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.createCachedDescriptors=function(dirname,options,alias){const{plugins,presets,passPerPreset}=options;return{options:optionsWithResolvedBrowserslistConfigFile(options,dirname),plugins:plugins?()=>createCachedPluginDescriptors(plugins,dirname)(alias):()=>handlerOf([]),presets:presets?()=>createCachedPresetDescriptors(presets,dirname)(alias)(!!passPerPreset):()=>handlerOf([])}},exports.createDescriptor=createDescriptor,exports.createUncachedDescriptors=function(dirname,options,alias){return{options:optionsWithResolvedBrowserslistConfigFile(options,dirname),plugins:(0,_functional.once)((()=>createPluginDescriptors(options.plugins||[],dirname,alias))),presets:(0,_functional.once)((()=>createPresetDescriptors(options.presets||[],dirname,alias,!!options.passPerPreset)))}};var _functional=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/functional.js"),_index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/index.js"),_item=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/item.js"),_caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js"),_resolveTargets=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/resolve-targets.js");function*handlerOf(value){return value}function optionsWithResolvedBrowserslistConfigFile(options,dirname){return"string"==typeof options.browserslistConfigFile&&(options.browserslistConfigFile=(0,_resolveTargets.resolveBrowserslistConfigFile)(options.browserslistConfigFile,dirname)),options}const PRESET_DESCRIPTOR_CACHE=new WeakMap,createCachedPresetDescriptors=(0,_caching.makeWeakCacheSync)(((items,cache)=>{const dirname=cache.using((dir=>dir));return(0,_caching.makeStrongCacheSync)((alias=>(0,_caching.makeStrongCache)((function*(passPerPreset){return(yield*createPresetDescriptors(items,dirname,alias,passPerPreset)).map((desc=>loadCachedDescriptor(PRESET_DESCRIPTOR_CACHE,desc)))}))))})),PLUGIN_DESCRIPTOR_CACHE=new WeakMap,createCachedPluginDescriptors=(0,_caching.makeWeakCacheSync)(((items,cache)=>{const dirname=cache.using((dir=>dir));return(0,_caching.makeStrongCache)((function*(alias){return(yield*createPluginDescriptors(items,dirname,alias)).map((desc=>loadCachedDescriptor(PLUGIN_DESCRIPTOR_CACHE,desc)))}))})),DEFAULT_OPTIONS={};function loadCachedDescriptor(cache,desc){const{value,options=DEFAULT_OPTIONS}=desc;if(!1===options)return desc;let cacheByOptions=cache.get(value);cacheByOptions||(cacheByOptions=new WeakMap,cache.set(value,cacheByOptions));let possibilities=cacheByOptions.get(options);if(possibilities||(possibilities=[],cacheByOptions.set(options,possibilities)),!possibilities.includes(desc)){const matches=possibilities.filter((possibility=>{return b=desc,(a=possibility).name===b.name&&a.value===b.value&&a.options===b.options&&a.dirname===b.dirname&&a.alias===b.alias&&a.ownPass===b.ownPass&&(null==(_a$file=a.file)?void 0:_a$file.request)===(null==(_b$file=b.file)?void 0:_b$file.request)&&(null==(_a$file2=a.file)?void 0:_a$file2.resolved)===(null==(_b$file2=b.file)?void 0:_b$file2.resolved);var a,b,_a$file,_b$file,_a$file2,_b$file2}));if(matches.length>0)return matches[0];possibilities.push(desc)}return desc}function*createPresetDescriptors(items,dirname,alias,passPerPreset){return yield*createDescriptors("preset",items,dirname,alias,passPerPreset)}function*createPluginDescriptors(items,dirname,alias){return yield*createDescriptors("plugin",items,dirname,alias)}function*createDescriptors(type,items,dirname,alias,ownPass){const descriptors=yield*_gensync().all(items.map(((item,index)=>createDescriptor(item,dirname,{type,alias:`${alias}$${index}`,ownPass:!!ownPass}))));return function(items){const map=new Map;for(const item of items){if("function"!=typeof item.value)continue;let nameMap=map.get(item.value);if(nameMap||(nameMap=new Set,map.set(item.value,nameMap)),nameMap.has(item.name)){const conflicts=items.filter((i=>i.value===item.value));throw new Error(["Duplicate plugin/preset detected.","If you'd like to use two separate instances of a plugin,","they need separate names, e.g.","","  plugins: [","    ['some-plugin', {}],","    ['some-plugin', {}, 'some unique name'],","  ]","","Duplicates detected are:",`${JSON.stringify(conflicts,null,2)}`].join("\n"))}nameMap.add(item.name)}}(descriptors),descriptors}function*createDescriptor(pair,dirname,{type,alias,ownPass}){const desc=(0,_item.getItemDescriptor)(pair);if(desc)return desc;let name,options,file,value=pair;Array.isArray(value)&&(3===value.length?[value,options,name]=value:[value,options]=value);let filepath=null;if("string"==typeof value){if("string"!=typeof type)throw new Error("To resolve a string-based item, the type of item must be given");const resolver="plugin"===type?_index.loadPlugin:_index.loadPreset,request=value;({filepath,value}=yield*resolver(value,dirname)),file={request,resolved:filepath}}if(!value)throw new Error(`Unexpected falsy value: ${String(value)}`);if("object"==typeof value&&value.__esModule){if(!value.default)throw new Error("Must export a default export when using ES6 modules.");value=value.default}if("object"!=typeof value&&"function"!=typeof value)throw new Error(`Unsupported format: ${typeof value}. Expected an object or a function.`);if(null!==filepath&&"object"==typeof value&&value)throw new Error(`Plugin/Preset files are not allowed to export objects, only functions. In ${filepath}`);return{name,alias:filepath||alias,value,options,dirname,ownPass,file}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/configuration.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _debug(){const data=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js");return _debug=function(){return data},data}function _fs(){const data=__webpack_require__("fs");return _fs=function(){return data},data}function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}function _json(){const data=__webpack_require__("./node_modules/.pnpm/json5@2.2.3/node_modules/json5/dist/index.mjs");return _json=function(){return data},data}function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.ROOT_CONFIG_FILENAMES=void 0,exports.findConfigUpwards=function(rootDir){let dirname=rootDir;for(;;){for(const filename of ROOT_CONFIG_FILENAMES)if(_fs().existsSync(_path().join(dirname,filename)))return dirname;const nextDir=_path().dirname(dirname);if(dirname===nextDir)break;dirname=nextDir}return null},exports.findRelativeConfig=function*(packageData,envName,caller){let config=null,ignore=null;const dirname=_path().dirname(packageData.filepath);for(const loc of packageData.directories){var _packageData$pkg;if(!config)config=yield*loadOneConfig(RELATIVE_CONFIG_FILENAMES,loc,envName,caller,(null==(_packageData$pkg=packageData.pkg)?void 0:_packageData$pkg.dirname)===loc?packageToBabelConfig(packageData.pkg):null);if(!ignore){const ignoreLoc=_path().join(loc,BABELIGNORE_FILENAME);ignore=yield*readIgnoreConfig(ignoreLoc),ignore&&debug("Found ignore %o from %o.",ignore.filepath,dirname)}}return{config,ignore}},exports.findRootConfig=function(dirname,envName,caller){return loadOneConfig(ROOT_CONFIG_FILENAMES,dirname,envName,caller)},exports.loadConfig=function*(name,dirname,envName,caller){const filepath=(v=process.versions.node,w="8.9",v=v.split("."),w=w.split("."),+v[0]>+w[0]||v[0]==w[0]&&+v[1]>=+w[1]?__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").resolve:(r,{paths:[b]},M=__webpack_require__("module"))=>{let f=M._findPath(r,M._nodeModulePaths(b).concat(b));if(f)return f;throw f=new Error(`Cannot resolve module '${r}'`),f.code="MODULE_NOT_FOUND",f})(name,{paths:[dirname]}),conf=yield*readConfig(filepath,envName,caller);var v,w;if(!conf)throw new _configError.default("Config file contains no configuration data",filepath);return debug("Loaded config %o from %o.",name,dirname),conf},exports.resolveShowConfigPath=function*(dirname){const targetPath=process.env.BABEL_SHOW_CONFIG_FOR;if(null!=targetPath){const absolutePath=_path().resolve(dirname,targetPath);if(!(yield*fs.stat(absolutePath)).isFile())throw new Error(`${absolutePath}: BABEL_SHOW_CONFIG_FOR must refer to a regular file, directories are not supported.`);return absolutePath}return null};var _caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js"),_configApi=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/config-api.js"),_utils=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/utils.js"),_moduleTypes=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/module-types.js"),_patternToRegex=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/pattern-to-regex.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js"),fs=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/fs.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js"),_async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js");const debug=_debug()("babel:config:loading:files:configuration"),ROOT_CONFIG_FILENAMES=exports.ROOT_CONFIG_FILENAMES=["babel.config.js","babel.config.cjs","babel.config.mjs","babel.config.json","babel.config.cts"],RELATIVE_CONFIG_FILENAMES=[".babelrc",".babelrc.js",".babelrc.cjs",".babelrc.mjs",".babelrc.json",".babelrc.cts"],BABELIGNORE_FILENAME=".babelignore",runConfig=(0,_caching.makeWeakCache)((function*(options,cache){return yield*[],{options:(0,_rewriteStackTrace.endHiddenCallStack)(options)((0,_configApi.makeConfigAPI)(cache)),cacheNeedsConfiguration:!cache.configured()}}));function*readConfigCode(filepath,data){if(!_fs().existsSync(filepath))return null;let options=yield*(0,_moduleTypes.default)(filepath,(yield*(0,_async.isAsync)())?"auto":"require","You appear to be using a native ECMAScript module configuration file, which is only supported when running Babel asynchronously or when using the Node.js `--experimental-require-module` flag.","You appear to be using a configuration file that contains top-level await, which is only supported when running Babel asynchronously."),cacheNeedsConfiguration=!1;if("function"==typeof options&&({options,cacheNeedsConfiguration}=yield*runConfig(options,data)),!options||"object"!=typeof options||Array.isArray(options))throw new _configError.default("Configuration should be an exported JavaScript object.",filepath);if("function"==typeof options.then)throw null==options.catch||options.catch((()=>{})),new _configError.default("You appear to be using an async configuration, which your current version of Babel does not support. We may add support for this in the future, but if you're on the most recent version of @babel/core and still seeing this error, then you'll need to synchronously return your config.",filepath);return cacheNeedsConfiguration&&function(filepath){throw new _configError.default('Caching was left unconfigured. Babel\'s plugins, presets, and .babelrc.js files can be configured\nfor various types of caching, using the first param of their handler functions:\n\nmodule.exports = function(api) {\n  // The API exposes the following:\n\n  // Cache the returned value forever and don\'t call this function again.\n  api.cache(true);\n\n  // Don\'t cache at all. Not recommended because it will be very slow.\n  api.cache(false);\n\n  // Cached based on the value of some function. If this function returns a value different from\n  // a previously-encountered value, the plugins will re-evaluate.\n  var env = api.cache(() => process.env.NODE_ENV);\n\n  // If testing for a specific env, we recommend specifics to avoid instantiating a plugin for\n  // any possible NODE_ENV value that might come up during plugin execution.\n  var isProd = api.cache(() => process.env.NODE_ENV === "production");\n\n  // .cache(fn) will perform a linear search though instances to find the matching plugin based\n  // based on previous instantiated plugins. If you want to recreate the plugin and discard the\n  // previous instance whenever something changes, you may use:\n  var isProd = api.cache.invalidate(() => process.env.NODE_ENV === "production");\n\n  // Note, we also expose the following more-verbose versions of the above examples:\n  api.cache.forever(); // api.cache(true)\n  api.cache.never();   // api.cache(false)\n  api.cache.using(fn); // api.cache(fn)\n\n  // Return the value that will be cached.\n  return { };\n};',filepath)}(filepath),function(options,filepath){let configFilesByFilepath=cfboaf.get(options);configFilesByFilepath||cfboaf.set(options,configFilesByFilepath=new Map);let configFile=configFilesByFilepath.get(filepath);configFile||(configFile={filepath,dirname:_path().dirname(filepath),options},configFilesByFilepath.set(filepath,configFile));return configFile}(options,filepath)}const cfboaf=new WeakMap;const packageToBabelConfig=(0,_caching.makeWeakCacheSync)((file=>{const babel=file.options.babel;if(void 0===babel)return null;if("object"!=typeof babel||Array.isArray(babel)||null===babel)throw new _configError.default(".babel property must be an object",file.filepath);return{filepath:file.filepath,dirname:file.dirname,options:babel}})),readConfigJSON5=(0,_utils.makeStaticFileCache)(((filepath,content)=>{let options;try{options=_json().parse(content)}catch(err){throw new _configError.default(`Error while parsing config - ${err.message}`,filepath)}if(!options)throw new _configError.default("No config detected",filepath);if("object"!=typeof options)throw new _configError.default("Config returned typeof "+typeof options,filepath);if(Array.isArray(options))throw new _configError.default("Expected config object but found array",filepath);return delete options.$schema,{filepath,dirname:_path().dirname(filepath),options}})),readIgnoreConfig=(0,_utils.makeStaticFileCache)(((filepath,content)=>{const ignoreDir=_path().dirname(filepath),ignorePatterns=content.split("\n").map((line=>line.replace(/#.*$/,"").trim())).filter(Boolean);for(const pattern of ignorePatterns)if("!"===pattern[0])throw new _configError.default("Negation of file paths is not supported.",filepath);return{filepath,dirname:_path().dirname(filepath),ignore:ignorePatterns.map((pattern=>(0,_patternToRegex.default)(pattern,ignoreDir)))}}));function*loadOneConfig(names,dirname,envName,caller,previousConfig=null){const config=(yield*_gensync().all(names.map((filename=>readConfig(_path().join(dirname,filename),envName,caller))))).reduce(((previousConfig,config)=>{if(config&&previousConfig)throw new _configError.default(`Multiple configuration files found. Please remove one:\n - ${_path().basename(previousConfig.filepath)}\n - ${config.filepath}\nfrom ${dirname}`);return config||previousConfig}),previousConfig);return config&&debug("Found configuration %o from %o.",config.filepath,dirname),config}function readConfig(filepath,envName,caller){switch(_path().extname(filepath)){case".js":case".cjs":case".mjs":case".cts":return readConfigCode(filepath,{envName,caller});default:return readConfigJSON5(filepath)}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/import.cjs":(module,__unused_webpack_exports,__webpack_require__)=>{module.exports=function(filepath){return __webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files lazy recursive")(filepath)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),Object.defineProperty(exports,"ROOT_CONFIG_FILENAMES",{enumerable:!0,get:function(){return _configuration.ROOT_CONFIG_FILENAMES}}),Object.defineProperty(exports,"findConfigUpwards",{enumerable:!0,get:function(){return _configuration.findConfigUpwards}}),Object.defineProperty(exports,"findPackageData",{enumerable:!0,get:function(){return _package.findPackageData}}),Object.defineProperty(exports,"findRelativeConfig",{enumerable:!0,get:function(){return _configuration.findRelativeConfig}}),Object.defineProperty(exports,"findRootConfig",{enumerable:!0,get:function(){return _configuration.findRootConfig}}),Object.defineProperty(exports,"loadConfig",{enumerable:!0,get:function(){return _configuration.loadConfig}}),Object.defineProperty(exports,"loadPlugin",{enumerable:!0,get:function(){return _plugins.loadPlugin}}),Object.defineProperty(exports,"loadPreset",{enumerable:!0,get:function(){return _plugins.loadPreset}}),Object.defineProperty(exports,"resolvePlugin",{enumerable:!0,get:function(){return _plugins.resolvePlugin}}),Object.defineProperty(exports,"resolvePreset",{enumerable:!0,get:function(){return _plugins.resolvePreset}}),Object.defineProperty(exports,"resolveShowConfigPath",{enumerable:!0,get:function(){return _configuration.resolveShowConfigPath}});var _package=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/package.js"),_configuration=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/configuration.js"),_plugins=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/plugins.js")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/module-types.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function*(filepath,loader,esmError,tlaError){var _async2;let async,ext=_path().extname(filepath);SUPPORTED_EXTENSIONS.has(ext)||(ext=".js");switch(`${loader} ${ext}`){case"require .cjs":case"auto .cjs":return loadCjsDefault(filepath,arguments[2]);case"require .cts":case"auto .cts":return function(filepath){const ext=".cts",hasTsSupport=!!(__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[".ts"]||__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[".cts"]||__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[".mts"]);let handler;if(!hasTsSupport){const opts={babelrc:!1,configFile:!1,sourceType:"unambiguous",sourceMaps:"inline",sourceFileName:_path().basename(filepath),presets:[[getTSPreset(filepath),Object.assign({onlyRemoveTypeImports:!0,optimizeConstEnums:!0},{allowDeclareFields:!0})]]};handler=function(m,filename){if(handler&&filename.endsWith(ext))try{return m._compile((0,_transformFile.transformFileSync)(filename,Object.assign({},opts,{filename})).code,filename)}catch(error){if(!hasTsSupport){const packageJson=__webpack_require__("./node_modules/.pnpm/@babel+preset-typescript@7.26.0_@babel+core@7.26.0/node_modules/@babel/preset-typescript/package.json");_semver().lt(packageJson.version,"7.21.4")&&console.error("`.cts` configuration file failed to load, please try to update `@babel/preset-typescript`.")}throw error}return __webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[".js"](m,filename)},__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[ext]=handler}try{return loadCjsDefault(filepath)}finally{hasTsSupport||(__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[ext]===handler&&delete __webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").extensions[ext],handler=void 0)}}(filepath);case"auto .js":case"require .js":case"require .mjs":try{return loadCjsDefault(filepath,arguments[2])}catch(e){var _async;if("ERR_REQUIRE_ASYNC_MODULE"===e.code||"ERR_REQUIRE_CYCLE_MODULE"===e.code&&asyncModules.has(filepath)){if(asyncModules.add(filepath),!(null!=(_async=async)?_async:async=yield*(0,_async3.isAsync)()))throw new _configError.default(tlaError,filepath)}else if("ERR_REQUIRE_ESM"!==e.code&&".mjs"!==ext)throw e}case"auto .mjs":if(null!=(_async2=async)?_async2:async=yield*(0,_async3.isAsync)())return(yield*(0,_async3.waitFor)(loadMjsFromPath(filepath))).default;throw new _configError.default(esmError,filepath);default:throw new Error("Internal Babel error: unreachable code.")}},exports.supportsESM=void 0;var _async3=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js");function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}function _url(){const data=__webpack_require__("url");return _url=function(){return data},data}function _semver(){const data=__webpack_require__("./node_modules/.pnpm/semver@6.3.1/node_modules/semver/semver.js");return _semver=function(){return data},data}function _debug(){const data=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js");return _debug=function(){return data},data}var _rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js"),_transformFile=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform-file.js");function asyncGeneratorStep(n,t,e,r,o,a,c){try{var i=n[a](c),u=i.value}catch(n){return void e(n)}i.done?t(u):Promise.resolve(u).then(r,o)}const debug=_debug()("babel:config:loading:files:module-types");try{var import_=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/import.cjs")}catch(_unused){}exports.supportsESM=_semver().satisfies(process.versions.node,"^12.17 || >=13.2");const LOADING_CJS_FILES=new Set;function loadCjsDefault(filepath){if(LOADING_CJS_FILES.has(filepath))return debug("Auto-ignoring usage of config %o.",filepath),{};let module;try{LOADING_CJS_FILES.add(filepath),module=(0,_rewriteStackTrace.endHiddenCallStack)(__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive"))(filepath)}finally{LOADING_CJS_FILES.delete(filepath)}return null==module||!module.__esModule&&"Module"!==module[Symbol.toStringTag]?module:module.default||(arguments[1]?module:void 0)}const loadMjsFromPath=(0,_rewriteStackTrace.endHiddenCallStack)((n=function*(filepath){const url=(0,_url().pathToFileURL)(filepath).toString()+"?import";if(!import_)throw new _configError.default("Internal error: Native ECMAScript modules aren't supported by this platform.\n",filepath);return yield import_(url)},_loadMjsFromPath=function(){var t=this,e=arguments;return new Promise((function(r,o){var a=n.apply(t,e);function _next(n){asyncGeneratorStep(a,r,o,_next,_throw,"next",n)}function _throw(n){asyncGeneratorStep(a,r,o,_next,_throw,"throw",n)}_next(void 0)}))},function(_x){return _loadMjsFromPath.apply(this,arguments)}));var n,_loadMjsFromPath;const SUPPORTED_EXTENSIONS=new Set([".js",".mjs",".cjs",".cts"]),asyncModules=new Set;function getTSPreset(filepath){try{return __webpack_require__("./node_modules/.pnpm/@babel+preset-typescript@7.26.0_@babel+core@7.26.0/node_modules/@babel/preset-typescript/lib/index.js")}catch(error){if("MODULE_NOT_FOUND"!==error.code)throw error;let message="You appear to be using a .cts file as Babel configuration, but the `@babel/preset-typescript` package was not found: please install it!";throw process.versions.pnp&&(message+='\nIf you are using Yarn Plug\'n\'Play, you may also need to add the following configuration to your .yarnrc.yml file:\n\npackageExtensions:\n\t"@babel/core@*":\n\t\tpeerDependencies:\n\t\t\t"@babel/preset-typescript": "*"\n'),new _configError.default(message,filepath)}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/package.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.findPackageData=function*(filepath){let pkg=null;const directories=[];let isPackage=!0,dirname=_path().dirname(filepath);for(;!pkg&&"node_modules"!==_path().basename(dirname);){directories.push(dirname),pkg=yield*readConfigPackage(_path().join(dirname,PACKAGE_FILENAME));const nextLoc=_path().dirname(dirname);if(dirname===nextLoc){isPackage=!1;break}dirname=nextLoc}return{filepath,directories,pkg,isPackage}};var _utils=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/utils.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js");const PACKAGE_FILENAME="package.json",readConfigPackage=(0,_utils.makeStaticFileCache)(((filepath,content)=>{let options;try{options=JSON.parse(content)}catch(err){throw new _configError.default(`Error while parsing JSON - ${err.message}`,filepath)}if(!options)throw new Error(`${filepath}: No config detected`);if("object"!=typeof options)throw new _configError.default("Config returned typeof "+typeof options,filepath);if(Array.isArray(options))throw new _configError.default("Expected config object but found array",filepath);return{filepath,dirname:_path().dirname(filepath),options}}))},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/plugins.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _debug(){const data=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js");return _debug=function(){return data},data}function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.loadPlugin=function*(name,dirname){const{filepath,loader}=resolvePlugin(name,dirname,yield*(0,_async.isAsync)()),value=yield*requireModule("plugin",loader,filepath);return debug("Loaded plugin %o from %o.",name,dirname),{filepath,value}},exports.loadPreset=function*(name,dirname){const{filepath,loader}=resolvePreset(name,dirname,yield*(0,_async.isAsync)()),value=yield*requireModule("preset",loader,filepath);return debug("Loaded preset %o from %o.",name,dirname),{filepath,value}},exports.resolvePreset=exports.resolvePlugin=void 0;var _async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js"),_moduleTypes=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/module-types.js");function _url(){const data=__webpack_require__("url");return _url=function(){return data},data}var _importMetaResolve=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/vendor/import-meta-resolve.js");function _fs(){const data=__webpack_require__("fs");return _fs=function(){return data},data}const debug=_debug()("babel:config:loading:files:plugins"),EXACT_RE=/^module:/,BABEL_PLUGIN_PREFIX_RE=/^(?!@|module:|[^/]+\/|babel-plugin-)/,BABEL_PRESET_PREFIX_RE=/^(?!@|module:|[^/]+\/|babel-preset-)/,BABEL_PLUGIN_ORG_RE=/^(@babel\/)(?!plugin-|[^/]+\/)/,BABEL_PRESET_ORG_RE=/^(@babel\/)(?!preset-|[^/]+\/)/,OTHER_PLUGIN_ORG_RE=/^(@(?!babel\/)[^/]+\/)(?![^/]*babel-plugin(?:-|\/|$)|[^/]+\/)/,OTHER_PRESET_ORG_RE=/^(@(?!babel\/)[^/]+\/)(?![^/]*babel-preset(?:-|\/|$)|[^/]+\/)/,OTHER_ORG_DEFAULT_RE=/^(@(?!babel$)[^/]+)$/,resolvePlugin=exports.resolvePlugin=resolveStandardizedName.bind(null,"plugin"),resolvePreset=exports.resolvePreset=resolveStandardizedName.bind(null,"preset");function standardizeName(type,name){if(_path().isAbsolute(name))return name;const isPreset="preset"===type;return name.replace(isPreset?BABEL_PRESET_PREFIX_RE:BABEL_PLUGIN_PREFIX_RE,`babel-${type}-`).replace(isPreset?BABEL_PRESET_ORG_RE:BABEL_PLUGIN_ORG_RE,`$1${type}-`).replace(isPreset?OTHER_PRESET_ORG_RE:OTHER_PLUGIN_ORG_RE,`$1babel-${type}-`).replace(OTHER_ORG_DEFAULT_RE,`$1/babel-${type}`).replace(EXACT_RE,"")}function*resolveAlternativesHelper(type,name){const standardizedName=standardizeName(type,name),{error,value}=yield standardizedName;if(!error)return value;if("MODULE_NOT_FOUND"!==error.code)throw error;standardizedName===name||(yield name).error||(error.message+=`\n- If you want to resolve "${name}", use "module:${name}"`),(yield standardizeName(type,"@babel/"+name)).error||(error.message+=`\n- Did you mean "@babel/${name}"?`);const oppositeType="preset"===type?"plugin":"preset";if((yield standardizeName(oppositeType,name)).error||(error.message+=`\n- Did you accidentally pass a ${oppositeType} as a ${type}?`),"plugin"===type){const transformName=standardizedName.replace("-proposal-","-transform-");transformName===standardizedName||(yield transformName).error||(error.message+=`\n- Did you mean "${transformName}"?`)}throw error.message+="\n\nMake sure that all the Babel plugins and presets you are using\nare defined as dependencies or devDependencies in your package.json\nfile. It's possible that the missing plugin is loaded by a preset\nyou are using that forgot to add the plugin to its dependencies: you\ncan workaround this problem by explicitly adding the missing package\nto your top-level package.json.\n",error}function tryRequireResolve(id,dirname){try{return dirname?{error:null,value:(v=process.versions.node,w="8.9",v=v.split("."),w=w.split("."),+v[0]>+w[0]||v[0]==w[0]&&+v[1]>=+w[1]?__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").resolve:(r,{paths:[b]},M=__webpack_require__("module"))=>{let f=M._findPath(r,M._nodeModulePaths(b).concat(b));if(f)return f;throw f=new Error(`Cannot resolve module '${r}'`),f.code="MODULE_NOT_FOUND",f})(id,{paths:[dirname]})}:{error:null,value:__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files sync recursive").resolve(id)}}catch(error){return{error,value:null}}var v,w}function tryImportMetaResolve(id,options){try{return{error:null,value:(0,_importMetaResolve.resolve)(id,options)}}catch(error){return{error,value:null}}}function resolveStandardizedNameForRequire(type,name,dirname){const it=resolveAlternativesHelper(type,name);let res=it.next();for(;!res.done;)res=it.next(tryRequireResolve(res.value,dirname));return{loader:"require",filepath:res.value}}function resolveStandardizedName(type,name,dirname,allowAsync){if(!_moduleTypes.supportsESM||!allowAsync)return resolveStandardizedNameForRequire(type,name,dirname);try{const resolved=function(type,name,dirname){const parentUrl=(0,_url().pathToFileURL)(_path().join(dirname,"./babel-virtual-resolve-base.js")).href,it=resolveAlternativesHelper(type,name);let res=it.next();for(;!res.done;)res=it.next(tryImportMetaResolve(res.value,parentUrl));return{loader:"auto",filepath:(0,_url().fileURLToPath)(res.value)}}(type,name,dirname);if(!(0,_fs().existsSync)(resolved.filepath))throw Object.assign(new Error(`Could not resolve "${name}" in file ${dirname}.`),{type:"MODULE_NOT_FOUND"});return resolved}catch(e){try{return resolveStandardizedNameForRequire(type,name,dirname)}catch(e2){if("MODULE_NOT_FOUND"===e.type)throw e;if("MODULE_NOT_FOUND"===e2.type)throw e2;throw e}}}var LOADING_MODULES=new Set;function*requireModule(type,loader,name){if(!(yield*(0,_async.isAsync)())&&LOADING_MODULES.has(name))throw new Error(`Reentrant ${type} detected trying to load "${name}". This module is not ignored and is trying to load itself while compiling itself, leading to a dependency cycle. We recommend adding it to your "ignore" list in your babelrc, or to a .babelignore.`);try{return LOADING_MODULES.add(name),yield*(0,_moduleTypes.default)(name,loader,`You appear to be using a native ECMAScript module ${type}, which is only supported when running Babel asynchronously or when using the Node.js \`--experimental-require-module\` flag.`,`You appear to be using a ${type} that contains top-level await, which is only supported when running Babel asynchronously.`,!0)}catch(err){throw err.message=`[BABEL]: ${err.message} (While processing: ${name})`,err}finally{LOADING_MODULES.delete(name)}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/utils.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeStaticFileCache=function(fn){return(0,_caching.makeStrongCache)((function*(filepath,cache){const cached=cache.invalidate((()=>function(filepath){if(!_fs2().existsSync(filepath))return null;try{return+_fs2().statSync(filepath).mtime}catch(e){if("ENOENT"!==e.code&&"ENOTDIR"!==e.code)throw e}return null}(filepath)));return null===cached?null:fn(filepath,yield*fs.readFile(filepath,"utf8"))}))};var _caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js"),fs=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/fs.js");function _fs2(){const data=__webpack_require__("fs");return _fs2=function(){return data},data}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/full.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js"),_util=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/util.js"),context=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/index.js"),_plugin=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/plugin.js"),_item=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/item.js"),_configChain=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-chain.js"),_deepArray=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/deep-array.js");function _traverse(){const data=__webpack_require__("./node_modules/.pnpm/@babel+traverse@7.26.4/node_modules/@babel/traverse/lib/index.js");return _traverse=function(){return data},data}var _caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js"),_options=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/options.js"),_plugins=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/plugins.js"),_configApi=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/config-api.js"),_partial=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/partial.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js");exports.default=_gensync()((function*(inputOpts){var _opts$assumptions;const result=yield*(0,_partial.default)(inputOpts);if(!result)return null;const{options,context,fileHandling}=result;if("ignored"===fileHandling)return null;const optionDefaults={},{plugins,presets}=options;if(!plugins||!presets)throw new Error("Assertion failure - plugins and presets exist");const presetContext=Object.assign({},context,{targets:options.targets}),toDescriptor=item=>{const desc=(0,_item.getItemDescriptor)(item);if(!desc)throw new Error("Assertion failure - must be config item");return desc},presetsDescriptors=presets.map(toDescriptor),initialPluginsDescriptors=plugins.map(toDescriptor),pluginDescriptorsByPass=[[]],passes=[],externalDependencies=[],ignored=yield*enhanceError(context,(function*recursePresetDescriptors(rawPresets,pluginDescriptorsPass){const presets=[];for(let i=0;i<rawPresets.length;i++){const descriptor=rawPresets[i];if(!1!==descriptor.options){try{var preset=yield*loadPresetDescriptor(descriptor,presetContext)}catch(e){throw"BABEL_UNKNOWN_OPTION"===e.code&&(0,_options.checkNoUnwrappedItemOptionPairs)(rawPresets,i,"preset",e),e}externalDependencies.push(preset.externalDependencies),descriptor.ownPass?presets.push({preset:preset.chain,pass:[]}):presets.unshift({preset:preset.chain,pass:pluginDescriptorsPass})}}if(presets.length>0){pluginDescriptorsByPass.splice(1,0,...presets.map((o=>o.pass)).filter((p=>p!==pluginDescriptorsPass)));for(const{preset,pass}of presets){if(!preset)return!0;pass.push(...preset.plugins);if(yield*recursePresetDescriptors(preset.presets,pass))return!0;preset.options.forEach((opts=>{(0,_util.mergeOptions)(optionDefaults,opts)}))}}}))(presetsDescriptors,pluginDescriptorsByPass[0]);if(ignored)return null;const opts=optionDefaults;(0,_util.mergeOptions)(opts,options);const pluginContext=Object.assign({},presetContext,{assumptions:null!=(_opts$assumptions=opts.assumptions)?_opts$assumptions:{}});return yield*enhanceError(context,(function*(){pluginDescriptorsByPass[0].unshift(...initialPluginsDescriptors);for(const descs of pluginDescriptorsByPass){const pass=[];passes.push(pass);for(let i=0;i<descs.length;i++){const descriptor=descs[i];if(!1!==descriptor.options){try{var plugin=yield*loadPluginDescriptor(descriptor,pluginContext)}catch(e){throw"BABEL_UNKNOWN_PLUGIN_PROPERTY"===e.code&&(0,_options.checkNoUnwrappedItemOptionPairs)(descs,i,"plugin",e),e}pass.push(plugin),externalDependencies.push(plugin.externalDependencies)}}}}))(),opts.plugins=passes[0],opts.presets=passes.slice(1).filter((plugins=>plugins.length>0)).map((plugins=>({plugins}))),opts.passPerPreset=opts.presets.length>0,{options:opts,passes,externalDependencies:(0,_deepArray.finalize)(externalDependencies)}}));function enhanceError(context,fn){return function*(arg1,arg2){try{return yield*fn(arg1,arg2)}catch(e){var _context$filename;if(!/^\[BABEL\]/.test(e.message))e.message=`[BABEL] ${null!=(_context$filename=context.filename)?_context$filename:"unknown file"}: ${e.message}`;throw e}}}const makeDescriptorLoader=apiFactory=>(0,_caching.makeWeakCache)((function*({value,options,dirname,alias},cache){if(!1===options)throw new Error("Assertion failure");options=options||{};const externalDependencies=[];let item=value;if("function"==typeof value){const factory=(0,_async.maybeAsync)(value,"You appear to be using an async plugin/preset, but Babel has been called synchronously"),api=Object.assign({},context,apiFactory(cache,externalDependencies));try{item=yield*factory(api,options,dirname)}catch(e){throw alias&&(e.message+=` (While processing: ${JSON.stringify(alias)})`),e}}if(!item||"object"!=typeof item)throw new Error("Plugin/Preset did not return an object.");if((0,_async.isThenable)(item))throw yield*[],new Error(`You appear to be using a promise as a plugin, which your current version of Babel does not support. If you're using a published plugin, you may need to upgrade your @babel/core version. As an alternative, you can prefix the promise with "await". (While processing: ${JSON.stringify(alias)})`);if(externalDependencies.length>0&&(!cache.configured()||"forever"===cache.mode())){let error=`A plugin/preset has external untracked dependencies (${externalDependencies[0]}), but the cache `;throw cache.configured()?error+=" has been configured to never be invalidated. ":error+="has not been configured to be invalidated when the external dependencies change. ",error+=`Plugins/presets should configure their cache to be invalidated when the external dependencies change, for example using \`api.cache.invalidate(() => statSync(filepath).mtimeMs)\` or \`api.cache.never()\`\n(While processing: ${JSON.stringify(alias)})`,new Error(error)}return{value:item,options,dirname,alias,externalDependencies:(0,_deepArray.finalize)(externalDependencies)}})),pluginDescriptorLoader=makeDescriptorLoader(_configApi.makePluginAPI),presetDescriptorLoader=makeDescriptorLoader(_configApi.makePresetAPI),instantiatePlugin=(0,_caching.makeWeakCache)((function*({value,options,dirname,alias,externalDependencies},cache){const pluginObj=(0,_plugins.validatePluginObject)(value),plugin=Object.assign({},pluginObj);if(plugin.visitor&&(plugin.visitor=_traverse().default.explode(Object.assign({},plugin.visitor))),plugin.inherits){const inheritsDescriptor={name:void 0,alias:`${alias}$inherits`,value:plugin.inherits,options,dirname},inherits=yield*(0,_async.forwardAsync)(loadPluginDescriptor,(run=>cache.invalidate((data=>run(inheritsDescriptor,data)))));plugin.pre=chainMaybeAsync(inherits.pre,plugin.pre),plugin.post=chainMaybeAsync(inherits.post,plugin.post),plugin.manipulateOptions=chainMaybeAsync(inherits.manipulateOptions,plugin.manipulateOptions),plugin.visitor=_traverse().default.visitors.merge([inherits.visitor||{},plugin.visitor||{}]),inherits.externalDependencies.length>0&&(externalDependencies=0===externalDependencies.length?inherits.externalDependencies:(0,_deepArray.finalize)([externalDependencies,inherits.externalDependencies]))}return new _plugin.default(plugin,options,alias,externalDependencies)}));function*loadPluginDescriptor(descriptor,context){if(descriptor.value instanceof _plugin.default){if(descriptor.options)throw new Error("Passed options to an existing Plugin instance will not work.");return descriptor.value}return yield*instantiatePlugin(yield*pluginDescriptorLoader(descriptor,context),context)}const needsFilename=val=>val&&"function"!=typeof val,validateIfOptionNeedsFilename=(options,descriptor)=>{if(needsFilename(options.test)||needsFilename(options.include)||needsFilename(options.exclude)){const formattedPresetName=descriptor.name?`"${descriptor.name}"`:"/* your preset */";throw new _configError.default([`Preset ${formattedPresetName} requires a filename to be set when babel is called directly,`,"```",`babel.transformSync(code, { filename: 'file.ts', presets: [${formattedPresetName}] });`,"```","See https://babeljs.io/docs/en/options#filename for more information."].join("\n"))}},validatePreset=(preset,context,descriptor)=>{if(!context.filename){var _options$overrides;const{options}=preset;validateIfOptionNeedsFilename(options,descriptor),null==(_options$overrides=options.overrides)||_options$overrides.forEach((overrideOptions=>validateIfOptionNeedsFilename(overrideOptions,descriptor)))}},instantiatePreset=(0,_caching.makeWeakCacheSync)((({value,dirname,alias,externalDependencies})=>({options:(0,_options.validate)("preset",value),alias,dirname,externalDependencies})));function*loadPresetDescriptor(descriptor,context){const preset=instantiatePreset(yield*presetDescriptorLoader(descriptor,context));return validatePreset(preset,context,descriptor),{chain:yield*(0,_configChain.buildPresetChain)(preset,context),externalDependencies:preset.externalDependencies}}function chainMaybeAsync(a,b){return a?b?function(...args){const res=a.apply(this,args);return res&&"function"==typeof res.then?res.then((()=>b.apply(this,args))):b.apply(this,args)}:a:b}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/config-api.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _semver(){const data=__webpack_require__("./node_modules/.pnpm/semver@6.3.1/node_modules/semver/semver.js");return _semver=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.makeConfigAPI=makeConfigAPI,exports.makePluginAPI=function(cache,externalDependencies){return Object.assign({},makePresetAPI(cache,externalDependencies),{assumption:name=>cache.using((data=>data.assumptions[name]))})},exports.makePresetAPI=makePresetAPI;var _index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/index.js"),_caching=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/caching.js");function makeConfigAPI(cache){return{version:_index.version,cache:cache.simple(),env:value=>cache.using((data=>void 0===value?data.envName:"function"==typeof value?(0,_caching.assertSimpleType)(value(data.envName)):(Array.isArray(value)?value:[value]).some((entry=>{if("string"!=typeof entry)throw new Error("Unexpected non-string value");return entry===data.envName})))),async:()=>!1,caller:cb=>cache.using((data=>(0,_caching.assertSimpleType)(cb(data.caller)))),assertVersion}}function makePresetAPI(cache,externalDependencies){return Object.assign({},makeConfigAPI(cache),{targets:()=>JSON.parse(cache.using((data=>JSON.stringify(data.targets)))),addExternalDependency:ref=>{externalDependencies.push(ref)}})}function assertVersion(range){if("number"==typeof range){if(!Number.isInteger(range))throw new Error("Expected string or integer value.");range=`^${range}.0.0-0`}if("string"!=typeof range)throw new Error("Expected string or integer value.");if("*"===range||_semver().satisfies(_index.version,range))return;const limit=Error.stackTraceLimit;"number"==typeof limit&&limit<25&&(Error.stackTraceLimit=25);const err=new Error(`Requires Babel "${range}", but was loaded with "${_index.version}". If you are sure you have a compatible version of @babel/core, it is likely that something in your build process is loading the wrong version. Inspect the stack trace of this error to look for the first entry that doesn't mention "@babel/core" or "babel-core" to see what is calling Babel.`);throw"number"==typeof limit&&(Error.stackTraceLimit=limit),Object.assign(err,{code:"BABEL_VERSION_UNSUPPORTED",version:_index.version,range})}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/deep-array.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.finalize=function(deepArr){return Object.freeze(deepArr)},exports.flattenToSet=function(arr){const result=new Set,stack=[arr];for(;stack.length>0;)for(const el of stack.pop())Array.isArray(el)?stack.push(el):result.add(el);return result}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/environment.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getEnv=function(defaultValue="development"){return process.env.BABEL_ENV||process.env.NODE_ENV||defaultValue}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.createConfigItem=function(target,options,callback){if(void 0!==callback)(0,_rewriteStackTrace.beginHiddenCallStack)(createConfigItemRunner.errback)(target,options,callback);else{if("function"!=typeof options)return createConfigItemSync(target,options);(0,_rewriteStackTrace.beginHiddenCallStack)(createConfigItemRunner.errback)(target,void 0,callback)}},exports.createConfigItemAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(createConfigItemRunner.async)(...args)},exports.createConfigItemSync=createConfigItemSync,Object.defineProperty(exports,"default",{enumerable:!0,get:function(){return _full.default}}),exports.loadOptions=function(opts,callback){if(void 0!==callback)(0,_rewriteStackTrace.beginHiddenCallStack)(loadOptionsRunner.errback)(opts,callback);else{if("function"!=typeof opts)return loadOptionsSync(opts);(0,_rewriteStackTrace.beginHiddenCallStack)(loadOptionsRunner.errback)(void 0,opts)}},exports.loadOptionsAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(loadOptionsRunner.async)(...args)},exports.loadOptionsSync=loadOptionsSync,exports.loadPartialConfig=function(opts,callback){if(void 0!==callback)(0,_rewriteStackTrace.beginHiddenCallStack)(loadPartialConfigRunner.errback)(opts,callback);else{if("function"!=typeof opts)return loadPartialConfigSync(opts);(0,_rewriteStackTrace.beginHiddenCallStack)(loadPartialConfigRunner.errback)(void 0,opts)}},exports.loadPartialConfigAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(loadPartialConfigRunner.async)(...args)},exports.loadPartialConfigSync=loadPartialConfigSync;var _full=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/full.js"),_partial=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/partial.js"),_item=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/item.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js");const loadPartialConfigRunner=_gensync()(_partial.loadPartialConfig);function loadPartialConfigSync(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(loadPartialConfigRunner.sync)(...args)}const loadOptionsRunner=_gensync()((function*(opts){var _config$options;const config=yield*(0,_full.default)(opts);return null!=(_config$options=null==config?void 0:config.options)?_config$options:null}));function loadOptionsSync(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(loadOptionsRunner.sync)(...args)}const createConfigItemRunner=_gensync()(_item.createConfigItem);function createConfigItemSync(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(createConfigItemRunner.sync)(...args)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/item.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.createConfigItem=function*(value,{dirname=".",type}={}){return createItemFromDescriptor(yield*(0,_configDescriptors.createDescriptor)(value,_path().resolve(dirname),{type,alias:"programmatic item"}))},exports.createItemFromDescriptor=createItemFromDescriptor,exports.getItemDescriptor=function(item){if(null!=item&&item[CONFIG_ITEM_BRAND])return item._descriptor;return};var _configDescriptors=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-descriptors.js");function createItemFromDescriptor(desc){return new ConfigItem(desc)}const CONFIG_ITEM_BRAND=Symbol.for("@babel/core@7 - ConfigItem");class ConfigItem{constructor(descriptor){this._descriptor=void 0,this[CONFIG_ITEM_BRAND]=!0,this.value=void 0,this.options=void 0,this.dirname=void 0,this.name=void 0,this.file=void 0,this._descriptor=descriptor,Object.defineProperty(this,"_descriptor",{enumerable:!1}),Object.defineProperty(this,CONFIG_ITEM_BRAND,{enumerable:!1}),this.value=this._descriptor.value,this.options=this._descriptor.options,this.dirname=this._descriptor.dirname,this.name=this._descriptor.name,this.file=this._descriptor.file?{request:this._descriptor.file.request,resolved:this._descriptor.file.resolved}:void 0,Object.freeze(this)}}Object.freeze(ConfigItem.prototype)},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/partial.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=loadPrivatePartialConfig,exports.loadPartialConfig=function*(opts){let showIgnoredFiles=!1;if("object"==typeof opts&&null!==opts&&!Array.isArray(opts)){var _opts=opts;({showIgnoredFiles}=_opts),opts=function(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}(_opts,_excluded)}const result=yield*loadPrivatePartialConfig(opts);if(!result)return null;const{options,babelrc,ignore,config,fileHandling,files}=result;if("ignored"===fileHandling&&!showIgnoredFiles)return null;return(options.plugins||[]).forEach((item=>{if(item.value instanceof _plugin.default)throw new Error("Passing cached plugin instances is not supported in babel.loadPartialConfig()")})),new PartialConfig(options,babelrc?babelrc.filepath:void 0,ignore?ignore.filepath:void 0,config?config.filepath:void 0,fileHandling,files)};var _plugin=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/plugin.js"),_util=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/util.js"),_item=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/item.js"),_configChain=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/config-chain.js"),_environment=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/environment.js"),_options=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/options.js"),_index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/index.js"),_resolveTargets=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/resolve-targets.js");const _excluded=["showIgnoredFiles"];function*loadPrivatePartialConfig(inputOpts){if(null!=inputOpts&&("object"!=typeof inputOpts||Array.isArray(inputOpts)))throw new Error("Babel options must be an object, null, or undefined");const args=inputOpts?(0,_options.validate)("arguments",inputOpts):{},{envName=(0,_environment.getEnv)(),cwd=".",root:rootDir=".",rootMode="root",caller,cloneInputAst=!0}=args,absoluteCwd=_path().resolve(cwd),absoluteRootDir=function(rootDir,rootMode){switch(rootMode){case"root":return rootDir;case"upward-optional":{const upwardRootDir=(0,_index.findConfigUpwards)(rootDir);return null===upwardRootDir?rootDir:upwardRootDir}case"upward":{const upwardRootDir=(0,_index.findConfigUpwards)(rootDir);if(null!==upwardRootDir)return upwardRootDir;throw Object.assign(new Error(`Babel was run with rootMode:"upward" but a root could not be found when searching upward from "${rootDir}".\nOne of the following config files must be in the directory tree: "${_index.ROOT_CONFIG_FILENAMES.join(", ")}".`),{code:"BABEL_ROOT_NOT_FOUND",dirname:rootDir})}default:throw new Error("Assertion failure - unknown rootMode value.")}}(_path().resolve(absoluteCwd,rootDir),rootMode),filename="string"==typeof args.filename?_path().resolve(cwd,args.filename):void 0,context={filename,cwd:absoluteCwd,root:absoluteRootDir,envName,caller,showConfig:(yield*(0,_index.resolveShowConfigPath)(absoluteCwd))===filename},configChain=yield*(0,_configChain.buildRootChain)(args,context);if(!configChain)return null;const merged={assumptions:{}};configChain.options.forEach((opts=>{(0,_util.mergeOptions)(merged,opts)}));return{options:Object.assign({},merged,{targets:(0,_resolveTargets.resolveTargets)(merged,absoluteRootDir),cloneInputAst,babelrc:!1,configFile:!1,browserslistConfigFile:!1,passPerPreset:!1,envName:context.envName,cwd:context.cwd,root:context.root,rootMode:"root",filename:"string"==typeof context.filename?context.filename:void 0,plugins:configChain.plugins.map((descriptor=>(0,_item.createItemFromDescriptor)(descriptor))),presets:configChain.presets.map((descriptor=>(0,_item.createItemFromDescriptor)(descriptor)))}),context,fileHandling:configChain.fileHandling,ignore:configChain.ignore,babelrc:configChain.babelrc,config:configChain.config,files:configChain.files}}class PartialConfig{constructor(options,babelrc,ignore,config,fileHandling,files){this.options=void 0,this.babelrc=void 0,this.babelignore=void 0,this.config=void 0,this.fileHandling=void 0,this.files=void 0,this.options=options,this.babelignore=ignore,this.babelrc=babelrc,this.config=config,this.fileHandling=fileHandling,this.files=files,Object.freeze(this)}hasFilesystemConfig(){return void 0!==this.babelrc||void 0!==this.config}}Object.freeze(PartialConfig.prototype)},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/pattern-to-regex.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(pattern,dirname){const parts=_path().resolve(dirname,pattern).split(_path().sep);return new RegExp(["^",...parts.map(((part,i)=>{const last=i===parts.length-1;return"**"===part?last?starStarPatLast:starStarPat:"*"===part?last?starPatLast:starPat:0===part.indexOf("*.")?substitution+escapeRegExp(part.slice(1))+(last?endSep:sep):escapeRegExp(part)+(last?endSep:sep)}))].join(""))};const sep=`\\${_path().sep}`,endSep=`(?:${sep}|$)`,substitution=`[^${sep}]+`,starPat=`(?:${substitution}${sep})`,starPatLast=`(?:${substitution}${endSep})`,starStarPat=`${starPat}*?`,starStarPatLast=`${starPat}*?${starPatLast}?`;function escapeRegExp(string){return string.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&")}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/plugin.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _deepArray=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/deep-array.js");exports.default=class{constructor(plugin,options,key,externalDependencies=(0,_deepArray.finalize)([])){this.key=void 0,this.manipulateOptions=void 0,this.post=void 0,this.pre=void 0,this.visitor=void 0,this.parserOverride=void 0,this.generatorOverride=void 0,this.options=void 0,this.externalDependencies=void 0,this.key=plugin.name||key,this.manipulateOptions=plugin.manipulateOptions,this.post=plugin.post,this.pre=plugin.pre,this.visitor=plugin.visitor||{},this.parserOverride=plugin.parserOverride,this.generatorOverride=plugin.generatorOverride,this.options=options,this.externalDependencies=externalDependencies}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/printer.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.ConfigPrinter=exports.ChainFormatter=void 0;const ChainFormatter=exports.ChainFormatter={Programmatic:0,Config:1},Formatter={title(type,callerName,filepath){let title="";return type===ChainFormatter.Programmatic?(title="programmatic options",callerName&&(title+=" from "+callerName)):title="config "+filepath,title},loc(index,envName){let loc="";return null!=index&&(loc+=`.overrides[${index}]`),null!=envName&&(loc+=`.env["${envName}"]`),loc},*optionsAndDescriptors(opt){const content=Object.assign({},opt.options);delete content.overrides,delete content.env;const pluginDescriptors=[...yield*opt.plugins()];pluginDescriptors.length&&(content.plugins=pluginDescriptors.map((d=>descriptorToConfig(d))));const presetDescriptors=[...yield*opt.presets()];return presetDescriptors.length&&(content.presets=[...presetDescriptors].map((d=>descriptorToConfig(d)))),JSON.stringify(content,void 0,2)}};function descriptorToConfig(d){var _d$file;let name=null==(_d$file=d.file)?void 0:_d$file.request;return null==name&&("object"==typeof d.value?name=d.value:"function"==typeof d.value&&(name=`[Function: ${d.value.toString().slice(0,50)} ... ]`)),null==name&&(name="[Unknown]"),void 0===d.options?name:null==d.name?[name,d.options]:[name,d.options,d.name]}class ConfigPrinter{constructor(){this._stack=[]}configure(enabled,type,{callerName,filepath}){return enabled?(content,index,envName)=>{this._stack.push({type,callerName,filepath,content,index,envName})}:()=>{}}static*format(config){let title=Formatter.title(config.type,config.callerName,config.filepath);const loc=Formatter.loc(config.index,config.envName);loc&&(title+=` ${loc}`);return`${title}\n${yield*Formatter.optionsAndDescriptors(config.content)}`}*output(){if(0===this._stack.length)return"";return(yield*_gensync().all(this._stack.map((s=>ConfigPrinter.format(s))))).join("\n\n")}}exports.ConfigPrinter=ConfigPrinter},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/resolve-targets.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}function _helperCompilationTargets(){const data=__webpack_require__("./stubs/helper-compilation-targets.mjs");return _helperCompilationTargets=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.resolveBrowserslistConfigFile=function(browserslistConfigFile,configFileDir){return _path().resolve(configFileDir,browserslistConfigFile)},exports.resolveTargets=function(options,root){const optTargets=options.targets;let targets;"string"==typeof optTargets||Array.isArray(optTargets)?targets={browsers:optTargets}:optTargets&&(targets="esmodules"in optTargets?Object.assign({},optTargets,{esmodules:"intersect"}):optTargets);const{browserslistConfigFile}=options;let configFile,ignoreBrowserslistConfig=!1;"string"==typeof browserslistConfigFile?configFile=browserslistConfigFile:ignoreBrowserslistConfig=!1===browserslistConfigFile;return(0,_helperCompilationTargets().default)(targets,{ignoreBrowserslistConfig,configFile,configPath:root,browserslistEnv:options.browserslistEnv})}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/util.js":(__unused_webpack_module,exports)=>{"use strict";function mergeDefaultFields(target,source){for(const k of Object.keys(source)){const val=source[k];void 0!==val&&(target[k]=val)}}Object.defineProperty(exports,"__esModule",{value:!0}),exports.isIterableIterator=function(value){return!!value&&"function"==typeof value.next&&"function"==typeof value[Symbol.iterator]},exports.mergeOptions=function(target,source){for(const k of Object.keys(source))if("parserOpts"!==k&&"generatorOpts"!==k&&"assumptions"!==k||!source[k]){const val=source[k];void 0!==val&&(target[k]=val)}else{const parserOpts=source[k];mergeDefaultFields(target[k]||(target[k]={}),parserOpts)}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/option-assertions.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _helperCompilationTargets(){const data=__webpack_require__("./stubs/helper-compilation-targets.mjs");return _helperCompilationTargets=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.access=access,exports.assertArray=assertArray,exports.assertAssumptions=function(loc,value){if(void 0===value)return;if("object"!=typeof value||null===value)throw new Error(`${msg(loc)} must be an object or undefined.`);let root=loc;do{root=root.parent}while("root"!==root.type);const inPreset="preset"===root.source;for(const name of Object.keys(value)){const subLoc=access(loc,name);if(!_options.assumptionsNames.has(name))throw new Error(`${msg(subLoc)} is not a supported assumption.`);if("boolean"!=typeof value[name])throw new Error(`${msg(subLoc)} must be a boolean.`);if(inPreset&&!1===value[name])throw new Error(`${msg(subLoc)} cannot be set to 'false' inside presets.`)}return value},exports.assertBabelrcSearch=function(loc,value){if(void 0===value||"boolean"==typeof value)return value;if(Array.isArray(value))value.forEach(((item,i)=>{if(!checkValidTest(item))throw new Error(`${msg(access(loc,i))} must be a string/Function/RegExp.`)}));else if(!checkValidTest(value))throw new Error(`${msg(loc)} must be a undefined, a boolean, a string/Function/RegExp or an array of those, got ${JSON.stringify(value)}`);return value},exports.assertBoolean=assertBoolean,exports.assertCallerMetadata=function(loc,value){const obj=assertObject(loc,value);if(obj){if("string"!=typeof obj.name)throw new Error(`${msg(loc)} set but does not contain "name" property string`);for(const prop of Object.keys(obj)){const propLoc=access(loc,prop),value=obj[prop];if(null!=value&&"boolean"!=typeof value&&"string"!=typeof value&&"number"!=typeof value)throw new Error(`${msg(propLoc)} must be null, undefined, a boolean, a string, or a number.`)}}return value},exports.assertCompact=function(loc,value){if(void 0!==value&&"boolean"!=typeof value&&"auto"!==value)throw new Error(`${msg(loc)} must be a boolean, "auto", or undefined`);return value},exports.assertConfigApplicableTest=function(loc,value){if(void 0===value)return value;if(Array.isArray(value))value.forEach(((item,i)=>{if(!checkValidTest(item))throw new Error(`${msg(access(loc,i))} must be a string/Function/RegExp.`)}));else if(!checkValidTest(value))throw new Error(`${msg(loc)} must be a string/Function/RegExp, or an array of those`);return value},exports.assertConfigFileSearch=function(loc,value){if(void 0!==value&&"boolean"!=typeof value&&"string"!=typeof value)throw new Error(`${msg(loc)} must be a undefined, a boolean, a string, got ${JSON.stringify(value)}`);return value},exports.assertFunction=function(loc,value){if(void 0!==value&&"function"!=typeof value)throw new Error(`${msg(loc)} must be a function, or undefined`);return value},exports.assertIgnoreList=function(loc,value){const arr=assertArray(loc,value);return null==arr||arr.forEach(((item,i)=>function(loc,value){if("string"!=typeof value&&"function"!=typeof value&&!(value instanceof RegExp))throw new Error(`${msg(loc)} must be an array of string/Function/RegExp values, or undefined`);return value}(access(loc,i),item))),arr},exports.assertInputSourceMap=function(loc,value){if(void 0!==value&&"boolean"!=typeof value&&("object"!=typeof value||!value))throw new Error(`${msg(loc)} must be a boolean, object, or undefined`);return value},exports.assertObject=assertObject,exports.assertPluginList=function(loc,value){const arr=assertArray(loc,value);arr&&arr.forEach(((item,i)=>function(loc,value){if(Array.isArray(value)){if(0===value.length)throw new Error(`${msg(loc)} must include an object`);if(value.length>3)throw new Error(`${msg(loc)} may only be a two-tuple or three-tuple`);if(assertPluginTarget(access(loc,0),value[0]),value.length>1){const opts=value[1];if(void 0!==opts&&!1!==opts&&("object"!=typeof opts||Array.isArray(opts)||null===opts))throw new Error(`${msg(access(loc,1))} must be an object, false, or undefined`)}if(3===value.length){const name=value[2];if(void 0!==name&&"string"!=typeof name)throw new Error(`${msg(access(loc,2))} must be a string, or undefined`)}}else assertPluginTarget(loc,value);return value}(access(loc,i),item)));return arr},exports.assertRootMode=function(loc,value){if(void 0!==value&&"root"!==value&&"upward"!==value&&"upward-optional"!==value)throw new Error(`${msg(loc)} must be a "root", "upward", "upward-optional" or undefined`);return value},exports.assertSourceMaps=function(loc,value){if(void 0!==value&&"boolean"!=typeof value&&"inline"!==value&&"both"!==value)throw new Error(`${msg(loc)} must be a boolean, "inline", "both", or undefined`);return value},exports.assertSourceType=function(loc,value){if(void 0!==value&&"module"!==value&&"script"!==value&&"unambiguous"!==value)throw new Error(`${msg(loc)} must be "module", "script", "unambiguous", or undefined`);return value},exports.assertString=function(loc,value){if(void 0!==value&&"string"!=typeof value)throw new Error(`${msg(loc)} must be a string, or undefined`);return value},exports.assertTargets=function(loc,value){if((0,_helperCompilationTargets().isBrowsersQueryValid)(value))return value;if("object"!=typeof value||!value||Array.isArray(value))throw new Error(`${msg(loc)} must be a string, an array of strings or an object`);const browsersLoc=access(loc,"browsers"),esmodulesLoc=access(loc,"esmodules");assertBrowsersList(browsersLoc,value.browsers),assertBoolean(esmodulesLoc,value.esmodules);for(const key of Object.keys(value)){const val=value[key],subLoc=access(loc,key);if("esmodules"===key)assertBoolean(subLoc,val);else if("browsers"===key)assertBrowsersList(subLoc,val);else{if(!hasOwnProperty.call(_helperCompilationTargets().TargetNames,key)){const validTargets=Object.keys(_helperCompilationTargets().TargetNames).join(", ");throw new Error(`${msg(subLoc)} is not a valid target. Supported targets are ${validTargets}`)}assertBrowserVersion(subLoc,val)}}return value},exports.msg=msg;var _options=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/options.js");function msg(loc){switch(loc.type){case"root":return"";case"env":return`${msg(loc.parent)}.env["${loc.name}"]`;case"overrides":return`${msg(loc.parent)}.overrides[${loc.index}]`;case"option":return`${msg(loc.parent)}.${loc.name}`;case"access":return`${msg(loc.parent)}[${JSON.stringify(loc.name)}]`;default:throw new Error(`Assertion failure: Unknown type ${loc.type}`)}}function access(loc,name){return{type:"access",name,parent:loc}}function assertBoolean(loc,value){if(void 0!==value&&"boolean"!=typeof value)throw new Error(`${msg(loc)} must be a boolean, or undefined`);return value}function assertObject(loc,value){if(void 0!==value&&("object"!=typeof value||Array.isArray(value)||!value))throw new Error(`${msg(loc)} must be an object, or undefined`);return value}function assertArray(loc,value){if(null!=value&&!Array.isArray(value))throw new Error(`${msg(loc)} must be an array, or undefined`);return value}function checkValidTest(value){return"string"==typeof value||"function"==typeof value||value instanceof RegExp}function assertPluginTarget(loc,value){if(("object"!=typeof value||!value)&&"string"!=typeof value&&"function"!=typeof value)throw new Error(`${msg(loc)} must be a string, object, function`);return value}function assertBrowsersList(loc,value){if(void 0!==value&&!(0,_helperCompilationTargets().isBrowsersQueryValid)(value))throw new Error(`${msg(loc)} must be undefined, a string or an array of strings`)}function assertBrowserVersion(loc,value){if(("number"!=typeof value||Math.round(value)!==value)&&"string"!=typeof value)throw new Error(`${msg(loc)} must be a string or an integer number`)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/options.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.assumptionsNames=void 0,exports.checkNoUnwrappedItemOptionPairs=function(items,index,type,e){if(0===index)return;const lastItem=items[index-1],thisItem=items[index];lastItem.file&&void 0===lastItem.options&&"object"==typeof thisItem.value&&(e.message+=`\n- Maybe you meant to use\n"${type}s": [\n  ["${lastItem.file.request}", ${JSON.stringify(thisItem.value,void 0,2)}]\n]\nTo be a valid ${type}, its name and options should be wrapped in a pair of brackets`)},exports.validate=function(type,opts,filename){try{return validateNested({type:"root",source:type},opts)}catch(error){const configError=new _configError.default(error.message,filename);throw error.code&&(configError.code=error.code),configError}};var _removed=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/removed.js"),_optionAssertions=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/option-assertions.js"),_configError=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js");const ROOT_VALIDATORS={cwd:_optionAssertions.assertString,root:_optionAssertions.assertString,rootMode:_optionAssertions.assertRootMode,configFile:_optionAssertions.assertConfigFileSearch,caller:_optionAssertions.assertCallerMetadata,filename:_optionAssertions.assertString,filenameRelative:_optionAssertions.assertString,code:_optionAssertions.assertBoolean,ast:_optionAssertions.assertBoolean,cloneInputAst:_optionAssertions.assertBoolean,envName:_optionAssertions.assertString},BABELRC_VALIDATORS={babelrc:_optionAssertions.assertBoolean,babelrcRoots:_optionAssertions.assertBabelrcSearch},NONPRESET_VALIDATORS={extends:_optionAssertions.assertString,ignore:_optionAssertions.assertIgnoreList,only:_optionAssertions.assertIgnoreList,targets:_optionAssertions.assertTargets,browserslistConfigFile:_optionAssertions.assertConfigFileSearch,browserslistEnv:_optionAssertions.assertString},COMMON_VALIDATORS={inputSourceMap:_optionAssertions.assertInputSourceMap,presets:_optionAssertions.assertPluginList,plugins:_optionAssertions.assertPluginList,passPerPreset:_optionAssertions.assertBoolean,assumptions:_optionAssertions.assertAssumptions,env:function(loc,value){if("env"===loc.parent.type)throw new Error(`${(0,_optionAssertions.msg)(loc)} is not allowed inside of another .env block`);const parent=loc.parent,obj=(0,_optionAssertions.assertObject)(loc,value);if(obj)for(const envName of Object.keys(obj)){const env=(0,_optionAssertions.assertObject)((0,_optionAssertions.access)(loc,envName),obj[envName]);if(!env)continue;validateNested({type:"env",name:envName,parent},env)}return obj},overrides:function(loc,value){if("env"===loc.parent.type)throw new Error(`${(0,_optionAssertions.msg)(loc)} is not allowed inside an .env block`);if("overrides"===loc.parent.type)throw new Error(`${(0,_optionAssertions.msg)(loc)} is not allowed inside an .overrides block`);const parent=loc.parent,arr=(0,_optionAssertions.assertArray)(loc,value);if(arr)for(const[index,item]of arr.entries()){const objLoc=(0,_optionAssertions.access)(loc,index),env=(0,_optionAssertions.assertObject)(objLoc,item);if(!env)throw new Error(`${(0,_optionAssertions.msg)(objLoc)} must be an object`);validateNested({type:"overrides",index,parent},env)}return arr},test:_optionAssertions.assertConfigApplicableTest,include:_optionAssertions.assertConfigApplicableTest,exclude:_optionAssertions.assertConfigApplicableTest,retainLines:_optionAssertions.assertBoolean,comments:_optionAssertions.assertBoolean,shouldPrintComment:_optionAssertions.assertFunction,compact:_optionAssertions.assertCompact,minified:_optionAssertions.assertBoolean,auxiliaryCommentBefore:_optionAssertions.assertString,auxiliaryCommentAfter:_optionAssertions.assertString,sourceType:_optionAssertions.assertSourceType,wrapPluginVisitorMethod:_optionAssertions.assertFunction,highlightCode:_optionAssertions.assertBoolean,sourceMaps:_optionAssertions.assertSourceMaps,sourceMap:_optionAssertions.assertSourceMaps,sourceFileName:_optionAssertions.assertString,sourceRoot:_optionAssertions.assertString,parserOpts:_optionAssertions.assertObject,generatorOpts:_optionAssertions.assertObject};Object.assign(COMMON_VALIDATORS,{getModuleId:_optionAssertions.assertFunction,moduleRoot:_optionAssertions.assertString,moduleIds:_optionAssertions.assertBoolean,moduleId:_optionAssertions.assertString});exports.assumptionsNames=new Set(["arrayLikeIsIterable","constantReexports","constantSuper","enumerableModuleMeta","ignoreFunctionLength","ignoreToPrimitiveHint","iterableIsArray","mutableTemplateObject","noClassCalls","noDocumentAll","noIncompleteNsImportDetection","noNewArrows","noUninitializedPrivateFieldAccess","objectRestNoSymbols","privateFieldsAsSymbols","privateFieldsAsProperties","pureGetters","setClassMethods","setComputedProperties","setPublicClassFields","setSpreadProperties","skipForOfIteratorClosing","superIsCallableConstructor"]);function getSource(loc){return"root"===loc.type?loc.source:getSource(loc.parent)}function validateNested(loc,opts){const type=getSource(loc);return function(opts){if(hasOwnProperty.call(opts,"sourceMap")&&hasOwnProperty.call(opts,"sourceMaps"))throw new Error(".sourceMap is an alias for .sourceMaps, cannot use both")}(opts),Object.keys(opts).forEach((key=>{const optLoc={type:"option",name:key,parent:loc};if("preset"===type&&NONPRESET_VALIDATORS[key])throw new Error(`${(0,_optionAssertions.msg)(optLoc)} is not allowed in preset options`);if("arguments"!==type&&ROOT_VALIDATORS[key])throw new Error(`${(0,_optionAssertions.msg)(optLoc)} is only allowed in root programmatic options`);if("arguments"!==type&&"configfile"!==type&&BABELRC_VALIDATORS[key]){if("babelrcfile"===type||"extendsfile"===type)throw new Error(`${(0,_optionAssertions.msg)(optLoc)} is not allowed in .babelrc or "extends"ed files, only in root programmatic options, or babel.config.js/config file options`);throw new Error(`${(0,_optionAssertions.msg)(optLoc)} is only allowed in root programmatic options, or babel.config.js/config file options`)}(COMMON_VALIDATORS[key]||NONPRESET_VALIDATORS[key]||BABELRC_VALIDATORS[key]||ROOT_VALIDATORS[key]||throwUnknownError)(optLoc,opts[key])})),opts}function throwUnknownError(loc){const key=loc.name;if(_removed.default[key]){const{message,version=5}=_removed.default[key];throw new Error(`Using removed Babel ${version} option: ${(0,_optionAssertions.msg)(loc)} - ${message}`)}{const unknownOptErr=new Error(`Unknown option: ${(0,_optionAssertions.msg)(loc)}. Check out https://babeljs.io/docs/en/babel-core/#options for more information about options.`);throw unknownOptErr.code="BABEL_UNKNOWN_OPTION",unknownOptErr}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/plugins.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.validatePluginObject=function(obj){const rootPath={type:"root",source:"plugin"};return Object.keys(obj).forEach((key=>{const validator=VALIDATORS[key];if(!validator){const invalidPluginPropertyError=new Error(`.${key} is not a valid Plugin property`);throw invalidPluginPropertyError.code="BABEL_UNKNOWN_PLUGIN_PROPERTY",invalidPluginPropertyError}validator({type:"option",name:key,parent:rootPath},obj[key])})),obj};var _optionAssertions=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/option-assertions.js");const VALIDATORS={name:_optionAssertions.assertString,manipulateOptions:_optionAssertions.assertFunction,pre:_optionAssertions.assertFunction,post:_optionAssertions.assertFunction,inherits:_optionAssertions.assertFunction,visitor:function(loc,value){const obj=(0,_optionAssertions.assertObject)(loc,value);if(obj&&(Object.keys(obj).forEach((prop=>{"_exploded"!==prop&&"_verified"!==prop&&function(key,value){if(value&&"object"==typeof value)Object.keys(value).forEach((handler=>{if("enter"!==handler&&"exit"!==handler)throw new Error(`.visitor["${key}"] may only have .enter and/or .exit handlers.`)}));else if("function"!=typeof value)throw new Error(`.visitor["${key}"] must be a function`)}(prop,obj[prop])})),obj.enter||obj.exit))throw new Error(`${(0,_optionAssertions.msg)(loc)} cannot contain catch-all "enter" or "exit" handlers. Please target individual nodes.`);return obj},parserOverride:_optionAssertions.assertFunction,generatorOverride:_optionAssertions.assertFunction}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/validation/removed.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;exports.default={auxiliaryComment:{message:"Use `auxiliaryCommentBefore` or `auxiliaryCommentAfter`"},blacklist:{message:"Put the specific transforms you want in the `plugins` option"},breakConfig:{message:"This is not a necessary option in Babel 6"},experimental:{message:"Put the specific transforms you want in the `plugins` option"},externalHelpers:{message:"Use the `external-helpers` plugin instead. Check out http://babeljs.io/docs/plugins/external-helpers/"},extra:{message:""},jsxPragma:{message:"use the `pragma` option in the `react-jsx` plugin. Check out http://babeljs.io/docs/plugins/transform-react-jsx/"},loose:{message:"Specify the `loose` option for the relevant plugin you are using or use a preset that sets the option."},metadataUsedHelpers:{message:"Not required anymore as this is enabled by default"},modules:{message:"Use the corresponding module transform plugin in the `plugins` option. Check out http://babeljs.io/docs/plugins/#modules"},nonStandard:{message:"Use the `react-jsx` and `flow-strip-types` plugins to support JSX and Flow. Also check out the react preset http://babeljs.io/docs/plugins/preset-react/"},optional:{message:"Put the specific transforms you want in the `plugins` option"},sourceMapName:{message:"The `sourceMapName` option has been removed because it makes more sense for the tooling that calls Babel to assign `map.file` themselves."},stage:{message:"Check out the corresponding stage-x presets http://babeljs.io/docs/plugins/#presets"},whitelist:{message:"Put the specific transforms you want in the `plugins` option"},resolveModuleSource:{version:6,message:"Use `babel-plugin-module-resolver@3`'s 'resolvePath' options"},metadata:{version:6,message:"Generated plugin metadata is always included in the output result"},sourceMapTarget:{version:6,message:"The `sourceMapTarget` option has been removed because it makes more sense for the tooling that calls Babel to assign `map.file` themselves."}}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/config-error.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js");class ConfigError extends Error{constructor(message,filename){super(message),(0,_rewriteStackTrace.expectedError)(this),filename&&(0,_rewriteStackTrace.injectVirtualStackFrame)(this,filename)}}exports.default=ConfigError},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js":(__unused_webpack_module,exports)=>{"use strict";var _Object$getOwnPropert;Object.defineProperty(exports,"__esModule",{value:!0}),exports.beginHiddenCallStack=function(fn){return SUPPORTED?Object.defineProperty((function(...args){return setupPrepareStackTrace(),fn(...args)}),"name",{value:STOP_HIDING}):fn},exports.endHiddenCallStack=function(fn){return SUPPORTED?Object.defineProperty((function(...args){return fn(...args)}),"name",{value:START_HIDING}):fn},exports.expectedError=function(error){if(!SUPPORTED)return;return expectedErrors.add(error),error},exports.injectVirtualStackFrame=function(error,filename){if(!SUPPORTED)return;let frames=virtualFrames.get(error);frames||virtualFrames.set(error,frames=[]);return frames.push(function(filename){return Object.create({isNative:()=>!1,isConstructor:()=>!1,isToplevel:()=>!0,getFileName:()=>filename,getLineNumber:()=>{},getColumnNumber:()=>{},getFunctionName:()=>{},getMethodName:()=>{},getTypeName:()=>{},toString:()=>filename})}(filename)),error};const ErrorToString=Function.call.bind(Error.prototype.toString),SUPPORTED=!!Error.captureStackTrace&&!0===(null==(_Object$getOwnPropert=Object.getOwnPropertyDescriptor(Error,"stackTraceLimit"))?void 0:_Object$getOwnPropert.writable),START_HIDING="startHiding - secret - don't use this - v1",STOP_HIDING="stopHiding - secret - don't use this - v1",expectedErrors=new WeakSet,virtualFrames=new WeakMap;function setupPrepareStackTrace(){setupPrepareStackTrace=()=>{};const{prepareStackTrace=defaultPrepareStackTrace}=Error;Error.stackTraceLimit&&(Error.stackTraceLimit=Math.max(Error.stackTraceLimit,50)),Error.prepareStackTrace=function(err,trace){let newTrace=[];let status=expectedErrors.has(err)?"hiding":"unknown";for(let i=0;i<trace.length;i++){const name=trace[i].getFunctionName();if(name===START_HIDING)status="hiding";else if(name===STOP_HIDING){if("hiding"===status)status="showing",virtualFrames.has(err)&&newTrace.unshift(...virtualFrames.get(err));else if("unknown"===status){newTrace=trace;break}}else"hiding"!==status&&newTrace.push(trace[i])}return prepareStackTrace(err,newTrace)}}function defaultPrepareStackTrace(err,trace){return 0===trace.length?ErrorToString(err):`${ErrorToString(err)}\n    at ${trace.join("\n    at ")}`}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}function asyncGeneratorStep(n,t,e,r,o,a,c){try{var i=n[a](c),u=i.value}catch(n){return void e(n)}i.done?t(u):Promise.resolve(u).then(r,o)}function _asyncToGenerator(n){return function(){var t=this,e=arguments;return new Promise((function(r,o){var a=n.apply(t,e);function _next(n){asyncGeneratorStep(a,r,o,_next,_throw,"next",n)}function _throw(n){asyncGeneratorStep(a,r,o,_next,_throw,"throw",n)}_next(void 0)}))}}Object.defineProperty(exports,"__esModule",{value:!0}),exports.forwardAsync=function(action,cb){const g=_gensync()(action);return withKind((kind=>{const adapted=g[kind];return cb(adapted)}))},exports.isAsync=void 0,exports.isThenable=isThenable,exports.maybeAsync=function(fn,message){return _gensync()({sync(...args){const result=fn.apply(this,args);if(isThenable(result))throw new Error(message);return result},async(...args){return Promise.resolve(fn.apply(this,args))}})},exports.waitFor=exports.onFirstPause=void 0;const runGenerator=_gensync()((function*(item){return yield*item}));exports.isAsync=_gensync()({sync:()=>!1,errback:cb=>cb(null,!0)});const withKind=_gensync()({sync:cb=>cb("sync"),async:(_ref=_asyncToGenerator((function*(cb){return cb("async")})),function(_x){return _ref.apply(this,arguments)})});var _ref;exports.onFirstPause=_gensync()({name:"onFirstPause",arity:2,sync:function(item){return runGenerator.sync(item)},errback:function(item,firstPause,cb){let completed=!1;runGenerator.errback(item,((err,value)=>{completed=!0,cb(err,value)})),completed||firstPause()}}),exports.waitFor=_gensync()({sync:x=>x,async:(_ref2=_asyncToGenerator((function*(x){return x})),function(_x2){return _ref2.apply(this,arguments)})});var _ref2;function isThenable(val){return!(!val||"object"!=typeof val&&"function"!=typeof val||!val.then||"function"!=typeof val.then)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/fs.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _fs(){const data=__webpack_require__("fs");return _fs=function(){return data},data}function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.stat=exports.readFile=void 0;exports.readFile=_gensync()({sync:_fs().readFileSync,errback:_fs().readFile}),exports.stat=_gensync()({sync:_fs().statSync,errback:_fs().stat})},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/functional.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.once=function(fn){let result,resultP,promiseReferenced=!1;return function*(){if(!result){if(resultP)return promiseReferenced=!0,yield*(0,_async.waitFor)(resultP);if(yield*(0,_async.isAsync)()){let resolve,reject;resultP=new Promise(((res,rej)=>{resolve=res,reject=rej}));try{result={ok:!0,value:yield*fn()},resultP=null,promiseReferenced&&resolve(result.value)}catch(error){result={ok:!1,value:error},resultP=null,promiseReferenced&&reject(error)}}else try{result={ok:!0,value:yield*fn()}}catch(error){result={ok:!1,value:error}}}if(result.ok)return result.value;throw result.value}};var _async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.DEFAULT_EXTENSIONS=void 0,Object.defineProperty(exports,"File",{enumerable:!0,get:function(){return _file.default}}),Object.defineProperty(exports,"buildExternalHelpers",{enumerable:!0,get:function(){return _buildExternalHelpers.default}}),Object.defineProperty(exports,"createConfigItem",{enumerable:!0,get:function(){return _index2.createConfigItem}}),Object.defineProperty(exports,"createConfigItemAsync",{enumerable:!0,get:function(){return _index2.createConfigItemAsync}}),Object.defineProperty(exports,"createConfigItemSync",{enumerable:!0,get:function(){return _index2.createConfigItemSync}}),Object.defineProperty(exports,"getEnv",{enumerable:!0,get:function(){return _environment.getEnv}}),Object.defineProperty(exports,"loadOptions",{enumerable:!0,get:function(){return _index2.loadOptions}}),Object.defineProperty(exports,"loadOptionsAsync",{enumerable:!0,get:function(){return _index2.loadOptionsAsync}}),Object.defineProperty(exports,"loadOptionsSync",{enumerable:!0,get:function(){return _index2.loadOptionsSync}}),Object.defineProperty(exports,"loadPartialConfig",{enumerable:!0,get:function(){return _index2.loadPartialConfig}}),Object.defineProperty(exports,"loadPartialConfigAsync",{enumerable:!0,get:function(){return _index2.loadPartialConfigAsync}}),Object.defineProperty(exports,"loadPartialConfigSync",{enumerable:!0,get:function(){return _index2.loadPartialConfigSync}}),Object.defineProperty(exports,"parse",{enumerable:!0,get:function(){return _parse.parse}}),Object.defineProperty(exports,"parseAsync",{enumerable:!0,get:function(){return _parse.parseAsync}}),Object.defineProperty(exports,"parseSync",{enumerable:!0,get:function(){return _parse.parseSync}}),exports.resolvePreset=exports.resolvePlugin=void 0,Object.defineProperty(exports,"template",{enumerable:!0,get:function(){return _template().default}}),Object.defineProperty(exports,"tokTypes",{enumerable:!0,get:function(){return _parser().tokTypes}}),Object.defineProperty(exports,"transform",{enumerable:!0,get:function(){return _transform.transform}}),Object.defineProperty(exports,"transformAsync",{enumerable:!0,get:function(){return _transform.transformAsync}}),Object.defineProperty(exports,"transformFile",{enumerable:!0,get:function(){return _transformFile.transformFile}}),Object.defineProperty(exports,"transformFileAsync",{enumerable:!0,get:function(){return _transformFile.transformFileAsync}}),Object.defineProperty(exports,"transformFileSync",{enumerable:!0,get:function(){return _transformFile.transformFileSync}}),Object.defineProperty(exports,"transformFromAst",{enumerable:!0,get:function(){return _transformAst.transformFromAst}}),Object.defineProperty(exports,"transformFromAstAsync",{enumerable:!0,get:function(){return _transformAst.transformFromAstAsync}}),Object.defineProperty(exports,"transformFromAstSync",{enumerable:!0,get:function(){return _transformAst.transformFromAstSync}}),Object.defineProperty(exports,"transformSync",{enumerable:!0,get:function(){return _transform.transformSync}}),Object.defineProperty(exports,"traverse",{enumerable:!0,get:function(){return _traverse().default}}),exports.version=exports.types=void 0;var _file=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/file.js"),_buildExternalHelpers=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/tools/build-external-helpers.js"),resolvers=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/files/index.js"),_environment=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/environment.js");function _types(){const data=__webpack_require__("./node_modules/.pnpm/@babel+types@7.26.3/node_modules/@babel/types/lib/index.js");return _types=function(){return data},data}function _parser(){const data=__webpack_require__("./node_modules/.pnpm/@babel+parser@7.26.3/node_modules/@babel/parser/lib/index.js");return _parser=function(){return data},data}function _traverse(){const data=__webpack_require__("./node_modules/.pnpm/@babel+traverse@7.26.4/node_modules/@babel/traverse/lib/index.js");return _traverse=function(){return data},data}function _template(){const data=__webpack_require__("./node_modules/.pnpm/@babel+template@7.25.9/node_modules/@babel/template/lib/index.js");return _template=function(){return data},data}Object.defineProperty(exports,"types",{enumerable:!0,get:function(){return _types()}});var _index2=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js"),_transform=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform.js"),_transformFile=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform-file.js"),_transformAst=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform-ast.js"),_parse=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parse.js");__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/index.js");exports.version="7.26.0";exports.resolvePlugin=(name,dirname)=>resolvers.resolvePlugin(name,dirname,!1).filepath;exports.resolvePreset=(name,dirname)=>resolvers.resolvePreset(name,dirname,!1).filepath;exports.DEFAULT_EXTENSIONS=Object.freeze([".js",".jsx",".es6",".es",".mjs",".cjs"]);exports.OptionManager=class{init(opts){return(0,_index2.loadOptionsSync)(opts)}},exports.Plugin=function(alias){throw new Error(`The (${alias}) Babel 5 plugin is being run with an unsupported Babel version.`)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parse.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.parse=void 0,exports.parseAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(parseRunner.async)(...args)},exports.parseSync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(parseRunner.sync)(...args)};var _index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js"),_index2=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parser/index.js"),_normalizeOpts=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/normalize-opts.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js");const parseRunner=_gensync()((function*(code,opts){const config=yield*(0,_index.default)(opts);return null===config?null:yield*(0,_index2.default)(config.passes,(0,_normalizeOpts.default)(config),code)}));exports.parse=function(code,opts,callback){if("function"==typeof opts&&(callback=opts,opts=void 0),void 0===callback)return(0,_rewriteStackTrace.beginHiddenCallStack)(parseRunner.sync)(code,opts);(0,_rewriteStackTrace.beginHiddenCallStack)(parseRunner.errback)(code,opts,callback)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parser/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _parser(){const data=__webpack_require__("./node_modules/.pnpm/@babel+parser@7.26.3/node_modules/@babel/parser/lib/index.js");return _parser=function(){return data},data}function _codeFrame(){const data=__webpack_require__("./stubs/babel-codeframe.mjs");return _codeFrame=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function*(pluginPasses,{parserOpts,highlightCode=!0,filename="unknown"},code){try{const results=[];for(const plugins of pluginPasses)for(const plugin of plugins){const{parserOverride}=plugin;if(parserOverride){const ast=parserOverride(code,parserOpts,_parser().parse);void 0!==ast&&results.push(ast)}}if(0===results.length)return(0,_parser().parse)(code,parserOpts);if(1===results.length){if(yield*[],"function"==typeof results[0].then)throw new Error("You appear to be using an async parser plugin, which your current version of Babel does not support. If you're using a published plugin, you may need to upgrade your @babel/core version.");return results[0]}throw new Error("More than one plugin attempted to override parsing.")}catch(err){"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"===err.code&&(err.message+="\nConsider renaming the file to '.mjs', or setting sourceType:module or sourceType:unambiguous in your Babel config for this file.");const{loc,missingPlugin}=err;if(loc){const codeFrame=(0,_codeFrame().codeFrameColumns)(code,{start:{line:loc.line,column:loc.column+1}},{highlightCode});err.message=missingPlugin?`${filename}: `+(0,_missingPluginHelper.default)(missingPlugin[0],loc,codeFrame,filename):`${filename}: ${err.message}\n\n`+codeFrame,err.code="BABEL_PARSE_ERROR"}throw err}};var _missingPluginHelper=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parser/util/missing-plugin-helper.js")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parser/util/missing-plugin-helper.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(missingPluginName,loc,codeFrame,filename){let helpMessage=`Support for the experimental syntax '${missingPluginName}' isn't currently enabled (${loc.line}:${loc.column+1}):\n\n`+codeFrame;const pluginInfo=pluginNameMap[missingPluginName];if(pluginInfo){const{syntax:syntaxPlugin,transform:transformPlugin}=pluginInfo;if(syntaxPlugin){const syntaxPluginInfo=getNameURLCombination(syntaxPlugin);if(transformPlugin){helpMessage+=`\n\nAdd ${getNameURLCombination(transformPlugin)} to the '${transformPlugin.name.startsWith("@babel/plugin")?"plugins":"presets"}' section of your Babel config to enable transformation.\nIf you want to leave it as-is, add ${syntaxPluginInfo} to the 'plugins' section to enable parsing.`}else helpMessage+=`\n\nAdd ${syntaxPluginInfo} to the 'plugins' section of your Babel config to enable parsing.`}}return helpMessage+=`\n\nIf you already added the plugin for this syntax to your config, it's possible that your config isn't being loaded.\nYou can re-run Babel with the BABEL_SHOW_CONFIG_FOR environment variable to show the loaded configuration:\n\tnpx cross-env BABEL_SHOW_CONFIG_FOR=${filename==="unknown"?"<name of the input file>":filename} <your build command>\nSee https://babeljs.io/docs/configuration#print-effective-configs for more info.\n`,helpMessage};const pluginNameMap={asyncDoExpressions:{syntax:{name:"@babel/plugin-syntax-async-do-expressions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-do-expressions"}},decimal:{syntax:{name:"@babel/plugin-syntax-decimal",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decimal"}},decorators:{syntax:{name:"@babel/plugin-syntax-decorators",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decorators"},transform:{name:"@babel/plugin-proposal-decorators",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-decorators"}},doExpressions:{syntax:{name:"@babel/plugin-syntax-do-expressions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-do-expressions"},transform:{name:"@babel/plugin-proposal-do-expressions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-do-expressions"}},exportDefaultFrom:{syntax:{name:"@babel/plugin-syntax-export-default-from",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-default-from"},transform:{name:"@babel/plugin-proposal-export-default-from",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-default-from"}},flow:{syntax:{name:"@babel/plugin-syntax-flow",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-flow"},transform:{name:"@babel/preset-flow",url:"https://github.com/babel/babel/tree/main/packages/babel-preset-flow"}},functionBind:{syntax:{name:"@babel/plugin-syntax-function-bind",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-bind"},transform:{name:"@babel/plugin-proposal-function-bind",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-bind"}},functionSent:{syntax:{name:"@babel/plugin-syntax-function-sent",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-sent"},transform:{name:"@babel/plugin-proposal-function-sent",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-sent"}},jsx:{syntax:{name:"@babel/plugin-syntax-jsx",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-jsx"},transform:{name:"@babel/preset-react",url:"https://github.com/babel/babel/tree/main/packages/babel-preset-react"}},pipelineOperator:{syntax:{name:"@babel/plugin-syntax-pipeline-operator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-pipeline-operator"},transform:{name:"@babel/plugin-proposal-pipeline-operator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-pipeline-operator"}},recordAndTuple:{syntax:{name:"@babel/plugin-syntax-record-and-tuple",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-record-and-tuple"}},throwExpressions:{syntax:{name:"@babel/plugin-syntax-throw-expressions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-throw-expressions"},transform:{name:"@babel/plugin-proposal-throw-expressions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-throw-expressions"}},typescript:{syntax:{name:"@babel/plugin-syntax-typescript",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-typescript"},transform:{name:"@babel/preset-typescript",url:"https://github.com/babel/babel/tree/main/packages/babel-preset-typescript"}}};Object.assign(pluginNameMap,{asyncGenerators:{syntax:{name:"@babel/plugin-syntax-async-generators",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-generators"},transform:{name:"@babel/plugin-transform-async-generator-functions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-async-generator-functions"}},classProperties:{syntax:{name:"@babel/plugin-syntax-class-properties",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties"},transform:{name:"@babel/plugin-transform-class-properties",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-class-properties"}},classPrivateProperties:{syntax:{name:"@babel/plugin-syntax-class-properties",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties"},transform:{name:"@babel/plugin-transform-class-properties",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-class-properties"}},classPrivateMethods:{syntax:{name:"@babel/plugin-syntax-class-properties",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties"},transform:{name:"@babel/plugin-transform-private-methods",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-private-methods"}},classStaticBlock:{syntax:{name:"@babel/plugin-syntax-class-static-block",url:"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-syntax-class-static-block"},transform:{name:"@babel/plugin-transform-class-static-block",url:"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-transform-class-static-block"}},dynamicImport:{syntax:{name:"@babel/plugin-syntax-dynamic-import",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-dynamic-import"}},exportNamespaceFrom:{syntax:{name:"@babel/plugin-syntax-export-namespace-from",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-namespace-from"},transform:{name:"@babel/plugin-transform-export-namespace-from",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-export-namespace-from"}},importAssertions:{syntax:{name:"@babel/plugin-syntax-import-assertions",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-assertions"}},importAttributes:{syntax:{name:"@babel/plugin-syntax-import-attributes",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-attributes"}},importMeta:{syntax:{name:"@babel/plugin-syntax-import-meta",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-meta"}},logicalAssignment:{syntax:{name:"@babel/plugin-syntax-logical-assignment-operators",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-logical-assignment-operators"},transform:{name:"@babel/plugin-transform-logical-assignment-operators",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-logical-assignment-operators"}},moduleStringNames:{syntax:{name:"@babel/plugin-syntax-module-string-names",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-module-string-names"}},numericSeparator:{syntax:{name:"@babel/plugin-syntax-numeric-separator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-numeric-separator"},transform:{name:"@babel/plugin-transform-numeric-separator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-numeric-separator"}},nullishCoalescingOperator:{syntax:{name:"@babel/plugin-syntax-nullish-coalescing-operator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-nullish-coalescing-operator"},transform:{name:"@babel/plugin-transform-nullish-coalescing-operator",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-nullish-coalescing-opearator"}},objectRestSpread:{syntax:{name:"@babel/plugin-syntax-object-rest-spread",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-object-rest-spread"},transform:{name:"@babel/plugin-transform-object-rest-spread",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-object-rest-spread"}},optionalCatchBinding:{syntax:{name:"@babel/plugin-syntax-optional-catch-binding",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-catch-binding"},transform:{name:"@babel/plugin-transform-optional-catch-binding",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-optional-catch-binding"}},optionalChaining:{syntax:{name:"@babel/plugin-syntax-optional-chaining",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-chaining"},transform:{name:"@babel/plugin-transform-optional-chaining",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-optional-chaining"}},privateIn:{syntax:{name:"@babel/plugin-syntax-private-property-in-object",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-private-property-in-object"},transform:{name:"@babel/plugin-transform-private-property-in-object",url:"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-private-property-in-object"}},regexpUnicodeSets:{syntax:{name:"@babel/plugin-syntax-unicode-sets-regex",url:"https://github.com/babel/babel/blob/main/packages/babel-plugin-syntax-unicode-sets-regex/README.md"},transform:{name:"@babel/plugin-transform-unicode-sets-regex",url:"https://github.com/babel/babel/blob/main/packages/babel-plugin-proposalunicode-sets-regex/README.md"}}});const getNameURLCombination=({name,url})=>`${name} (${url})`},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/tools/build-external-helpers.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function helpers(){const data=__webpack_require__("./node_modules/.pnpm/@babel+helpers@7.26.0/node_modules/@babel/helpers/lib/index.js");return helpers=function(){return data},data}function _generator(){const data=__webpack_require__("./node_modules/.pnpm/@babel+generator@7.26.3/node_modules/@babel/generator/lib/index.js");return _generator=function(){return data},data}function _template(){const data=__webpack_require__("./node_modules/.pnpm/@babel+template@7.25.9/node_modules/@babel/template/lib/index.js");return _template=function(){return data},data}function _t(){const data=__webpack_require__("./node_modules/.pnpm/@babel+types@7.26.3/node_modules/@babel/types/lib/index.js");return _t=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(allowlist,outputType="global"){let tree;const build={global:buildGlobal,module:buildModule,umd:buildUmd,var:buildVar}[outputType];if(!build)throw new Error(`Unsupported output type ${outputType}`);tree=build(allowlist);return(0,_generator().default)(tree).code};const{arrayExpression,assignmentExpression,binaryExpression,blockStatement,callExpression,cloneNode,conditionalExpression,exportNamedDeclaration,exportSpecifier,expressionStatement,functionExpression,identifier,memberExpression,objectExpression,program,stringLiteral,unaryExpression,variableDeclaration,variableDeclarator}=_t(),buildUmdWrapper=replacements=>_template().default.statement`
    (function (root, factory) {
      if (typeof define === "function" && define.amd) {
        define(AMD_ARGUMENTS, factory);
      } else if (typeof exports === "object") {
        factory(COMMON_ARGUMENTS);
      } else {
        factory(BROWSER_ARGUMENTS);
      }
    })(UMD_ROOT, function (FACTORY_PARAMETERS) {
      FACTORY_BODY
    });
  `(replacements);function buildGlobal(allowlist){const namespace=identifier("babelHelpers"),body=[],container=functionExpression(null,[identifier("global")],blockStatement(body)),tree=program([expressionStatement(callExpression(container,[conditionalExpression(binaryExpression("===",unaryExpression("typeof",identifier("global")),stringLiteral("undefined")),identifier("self"),identifier("global"))]))]);return body.push(variableDeclaration("var",[variableDeclarator(namespace,assignmentExpression("=",memberExpression(identifier("global"),namespace),objectExpression([])))])),buildHelpers(body,namespace,allowlist),tree}function buildModule(allowlist){const body=[],refs=buildHelpers(body,null,allowlist);return body.unshift(exportNamedDeclaration(null,Object.keys(refs).map((name=>exportSpecifier(cloneNode(refs[name]),identifier(name)))))),program(body,[],"module")}function buildUmd(allowlist){const namespace=identifier("babelHelpers"),body=[];return body.push(variableDeclaration("var",[variableDeclarator(namespace,identifier("global"))])),buildHelpers(body,namespace,allowlist),program([buildUmdWrapper({FACTORY_PARAMETERS:identifier("global"),BROWSER_ARGUMENTS:assignmentExpression("=",memberExpression(identifier("root"),namespace),objectExpression([])),COMMON_ARGUMENTS:identifier("exports"),AMD_ARGUMENTS:arrayExpression([stringLiteral("exports")]),FACTORY_BODY:body,UMD_ROOT:identifier("this")})])}function buildVar(allowlist){const namespace=identifier("babelHelpers"),body=[];body.push(variableDeclaration("var",[variableDeclarator(namespace,objectExpression([]))]));const tree=program(body);return buildHelpers(body,namespace,allowlist),body.push(expressionStatement(namespace)),tree}function buildHelpers(body,namespace,allowlist){const getHelperReference=name=>namespace?memberExpression(namespace,identifier(name)):identifier(`_${name}`),refs={};return helpers().list.forEach((function(name){if(allowlist&&!allowlist.includes(name))return;const ref=refs[name]=getHelperReference(name),{nodes}=helpers().get(name,getHelperReference,namespace?null:`_${name}`,[],namespace?(ast,exportName,mapExportBindingAssignments)=>{mapExportBindingAssignments((node=>assignmentExpression("=",ref,node))),ast.body.push(expressionStatement(assignmentExpression("=",ref,identifier(exportName))))}:null);body.push(...nodes)})),refs}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform-ast.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.transformFromAst=void 0,exports.transformFromAstAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(transformFromAstRunner.async)(...args)},exports.transformFromAstSync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(transformFromAstRunner.sync)(...args)};var _index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js"),_index2=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/index.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js");const transformFromAstRunner=_gensync()((function*(ast,code,opts){const config=yield*(0,_index.default)(opts);if(null===config)return null;if(!ast)throw new Error("No AST given");return yield*(0,_index2.run)(config,code,ast)}));exports.transformFromAst=function(ast,code,optsOrCallback,maybeCallback){let opts,callback;if("function"==typeof optsOrCallback?(callback=optsOrCallback,opts=void 0):(opts=optsOrCallback,callback=maybeCallback),void 0===callback)return(0,_rewriteStackTrace.beginHiddenCallStack)(transformFromAstRunner.sync)(ast,code,opts);(0,_rewriteStackTrace.beginHiddenCallStack)(transformFromAstRunner.errback)(ast,code,opts,callback)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform-file.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.transformFile=function(...args){transformFileRunner.errback(...args)},exports.transformFileAsync=function(...args){return transformFileRunner.async(...args)},exports.transformFileSync=function(...args){return transformFileRunner.sync(...args)};var _index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js"),_index2=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/index.js"),fs=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/fs.js");const transformFileRunner=_gensync()((function*(filename,opts){const options=Object.assign({},opts,{filename}),config=yield*(0,_index.default)(options);if(null===config)return null;const code=yield*fs.readFile(filename,"utf8");return yield*(0,_index2.run)(config,code)}))},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transform.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _gensync(){const data=__webpack_require__("./node_modules/.pnpm/gensync@1.0.0-beta.2/node_modules/gensync/index.js");return _gensync=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.transform=void 0,exports.transformAsync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(transformRunner.async)(...args)},exports.transformSync=function(...args){return(0,_rewriteStackTrace.beginHiddenCallStack)(transformRunner.sync)(...args)};var _index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/index.js"),_index2=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/index.js"),_rewriteStackTrace=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/errors/rewrite-stack-trace.js");const transformRunner=_gensync()((function*(code,opts){const config=yield*(0,_index.default)(opts);return null===config?null:yield*(0,_index2.run)(config,code)}));exports.transform=function(code,optsOrCallback,maybeCallback){let opts,callback;if("function"==typeof optsOrCallback?(callback=optsOrCallback,opts=void 0):(opts=optsOrCallback,callback=maybeCallback),void 0===callback)return(0,_rewriteStackTrace.beginHiddenCallStack)(transformRunner.sync)(code,opts);(0,_rewriteStackTrace.beginHiddenCallStack)(transformRunner.errback)(code,opts,callback)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/block-hoist-plugin.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _traverse(){const data=__webpack_require__("./node_modules/.pnpm/@babel+traverse@7.26.4/node_modules/@babel/traverse/lib/index.js");return _traverse=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(){LOADED_PLUGIN||(LOADED_PLUGIN=new _plugin.default(Object.assign({},blockHoistPlugin,{visitor:_traverse().default.explode(blockHoistPlugin.visitor)}),{}));return LOADED_PLUGIN};var _plugin=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/plugin.js");let LOADED_PLUGIN;const blockHoistPlugin={name:"internal.blockHoist",visitor:{Block:{exit({node}){node.body=performHoisting(node.body)}},SwitchCase:{exit({node}){node.consequent=performHoisting(node.consequent)}}}};function performHoisting(body){let max=Math.pow(2,30)-1,hasChange=!1;for(let i=0;i<body.length;i++){const p=priority(body[i]);if(p>max){hasChange=!0;break}max=p}return hasChange?function(body){const buckets=Object.create(null);for(let i=0;i<body.length;i++){const n=body[i],p=priority(n);(buckets[p]||(buckets[p]=[])).push(n)}const keys=Object.keys(buckets).map((k=>+k)).sort(((a,b)=>b-a));let index=0;for(const key of keys){const bucket=buckets[key];for(const n of bucket)body[index++]=n}return body}(body.slice()):body}function priority(bodyNode){const priority=null==bodyNode?void 0:bodyNode._blockHoist;return null==priority?1:!0===priority?2:priority}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/babel-7-helpers.cjs":(__unused_webpack_module,exports,__webpack_require__)=>{exports.getModuleName=()=>__webpack_require__("./node_modules/.pnpm/@babel+helper-module-transforms@7.26.0_@babel+core@7.26.0/node_modules/@babel/helper-module-transforms/lib/index.js").getModuleName},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/file.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function helpers(){const data=__webpack_require__("./node_modules/.pnpm/@babel+helpers@7.26.0/node_modules/@babel/helpers/lib/index.js");return helpers=function(){return data},data}function _traverse(){const data=__webpack_require__("./node_modules/.pnpm/@babel+traverse@7.26.4/node_modules/@babel/traverse/lib/index.js");return _traverse=function(){return data},data}function _codeFrame(){const data=__webpack_require__("./stubs/babel-codeframe.mjs");return _codeFrame=function(){return data},data}function _t(){const data=__webpack_require__("./node_modules/.pnpm/@babel+types@7.26.3/node_modules/@babel/types/lib/index.js");return _t=function(){return data},data}function _semver(){const data=__webpack_require__("./node_modules/.pnpm/semver@6.3.1/node_modules/semver/semver.js");return _semver=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var babel7=function(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,t&&t.set(e,n),n}(__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/babel-7-helpers.cjs"),!0);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(_getRequireWildcardCache=function(e){return e?t:r})(e)}const{cloneNode,interpreterDirective}=_t(),errorVisitor={enter(path,state){const loc=path.node.loc;loc&&(state.loc=loc,path.stop())}};class File{constructor(options,{code,ast,inputMap}){this._map=new Map,this.opts=void 0,this.declarations={},this.path=void 0,this.ast=void 0,this.scope=void 0,this.metadata={},this.code="",this.inputMap=void 0,this.hub={file:this,getCode:()=>this.code,getScope:()=>this.scope,addHelper:this.addHelper.bind(this),buildError:this.buildCodeFrameError.bind(this)},this.opts=options,this.code=code,this.ast=ast,this.inputMap=inputMap,this.path=_traverse().NodePath.get({hub:this.hub,parentPath:null,parent:this.ast,container:this.ast,key:"program"}).setContext(),this.scope=this.path.scope}get shebang(){const{interpreter}=this.path.node;return interpreter?interpreter.value:""}set shebang(value){value?this.path.get("interpreter").replaceWith(interpreterDirective(value)):this.path.get("interpreter").remove()}set(key,val){if("helpersNamespace"===key)throw new Error("Babel 7.0.0-beta.56 has dropped support for the 'helpersNamespace' utility.If you are using @babel/plugin-external-helpers you will need to use a newer version than the one you currently have installed. If you have your own implementation, you'll want to explore using 'helperGenerator' alongside 'file.availableHelper()'.");this._map.set(key,val)}get(key){return this._map.get(key)}has(key){return this._map.has(key)}availableHelper(name,versionRange){let minVersion;try{minVersion=helpers().minVersion(name)}catch(err){if("BABEL_HELPER_UNKNOWN"!==err.code)throw err;return!1}return"string"!=typeof versionRange||(_semver().valid(versionRange)&&(versionRange=`^${versionRange}`),!_semver().intersects(`<${minVersion}`,versionRange)&&!_semver().intersects(">=8.0.0",versionRange))}addHelper(name){const declar=this.declarations[name];if(declar)return cloneNode(declar);const generator=this.get("helperGenerator");if(generator){const res=generator(name);if(res)return res}helpers().minVersion(name);const uid=this.declarations[name]=this.scope.generateUidIdentifier(name),dependencies={};for(const dep of helpers().getDependencies(name))dependencies[dep]=this.addHelper(dep);const{nodes,globals}=helpers().get(name,(dep=>dependencies[dep]),uid.name,Object.keys(this.scope.getAllBindings()));globals.forEach((name=>{this.path.scope.hasBinding(name,!0)&&this.path.scope.rename(name)})),nodes.forEach((node=>{node._compact=!0}));const added=this.path.unshiftContainer("body",nodes);for(const path of added)path.isVariableDeclaration()&&this.scope.registerDeclaration(path);return uid}buildCodeFrameError(node,msg,_Error=SyntaxError){let loc=null==node?void 0:node.loc;if(!loc&&node){const state={loc:null};(0,_traverse().default)(node,errorVisitor,this.scope,state),loc=state.loc;let txt="This is an error on an internal node. Probably an internal error.";loc&&(txt+=" Location has been estimated."),msg+=` (${txt})`}if(loc){const{highlightCode=!0}=this.opts;msg+="\n"+(0,_codeFrame().codeFrameColumns)(this.code,{start:{line:loc.start.line,column:loc.start.column+1},end:loc.end&&loc.start.line===loc.end.line?{line:loc.end.line,column:loc.end.column+1}:void 0},{highlightCode})}return new _Error(msg)}}exports.default=File,File.prototype.addImport=function(){throw new Error("This API has been removed. If you're looking for this functionality in Babel 7, you should import the '@babel/helper-module-imports' module and use the functions exposed  from that module, such as 'addNamed' or 'addDefault'.")},File.prototype.addTemplateObject=function(){throw new Error("This function has been moved into the template literal transform itself.")},File.prototype.getModuleName=function(){return babel7.getModuleName()(this.opts,this.opts)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/generate.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _convertSourceMap(){const data=__webpack_require__("./node_modules/.pnpm/convert-source-map@2.0.0/node_modules/convert-source-map/index.js");return _convertSourceMap=function(){return data},data}function _generator(){const data=__webpack_require__("./node_modules/.pnpm/@babel+generator@7.26.3/node_modules/@babel/generator/lib/index.js");return _generator=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(pluginPasses,file){const{opts,ast,code,inputMap}=file,{generatorOpts}=opts;generatorOpts.inputSourceMap=null==inputMap?void 0:inputMap.toObject();const results=[];for(const plugins of pluginPasses)for(const plugin of plugins){const{generatorOverride}=plugin;if(generatorOverride){const result=generatorOverride(ast,generatorOpts,code,_generator().default);void 0!==result&&results.push(result)}}let result;if(0===results.length)result=(0,_generator().default)(ast,generatorOpts,code);else{if(1!==results.length)throw new Error("More than one plugin attempted to override codegen.");if(result=results[0],"function"==typeof result.then)throw new Error("You appear to be using an async codegen plugin, which your current version of Babel does not support. If you're using a published plugin, you may need to upgrade your @babel/core version.")}let{code:outputCode,decodedMap:outputMap=result.map}=result;result.__mergedMap?outputMap=Object.assign({},result.map):outputMap&&(outputMap=inputMap?(0,_mergeMap.default)(inputMap.toObject(),outputMap,generatorOpts.sourceFileName):result.map);"inline"!==opts.sourceMaps&&"both"!==opts.sourceMaps||(outputCode+="\n"+_convertSourceMap().fromObject(outputMap).toComment());"inline"===opts.sourceMaps&&(outputMap=null);return{outputCode,outputMap}};var _mergeMap=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/merge-map.js")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/merge-map.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _remapping(){const data=__webpack_require__("./node_modules/.pnpm/@ampproject+remapping@2.3.0/node_modules/@ampproject/remapping/dist/remapping.umd.js");return _remapping=function(){return data},data}function rootless(map){return Object.assign({},map,{sourceRoot:null})}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(inputMap,map,sourceFileName){const source=sourceFileName.replace(/\\/g,"/");let found=!1;const result=_remapping()(rootless(map),((s,ctx)=>s!==source||found?null:(found=!0,ctx.source="",rootless(inputMap))));"string"==typeof inputMap.sourceRoot&&(result.sourceRoot=inputMap.sourceRoot);return Object.assign({},result)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/index.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _traverse(){const data=__webpack_require__("./node_modules/.pnpm/@babel+traverse@7.26.4/node_modules/@babel/traverse/lib/index.js");return _traverse=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.run=function*(config,code,ast){const file=yield*(0,_normalizeFile.default)(config.passes,(0,_normalizeOpts.default)(config),code,ast),opts=file.opts;try{yield*function*(file,pluginPasses){const async=yield*(0,_async.isAsync)();for(const pluginPairs of pluginPasses){const passPairs=[],passes=[],visitors=[];for(const plugin of pluginPairs.concat([(0,_blockHoistPlugin.default)()])){const pass=new _pluginPass.default(file,plugin.key,plugin.options,async);passPairs.push([plugin,pass]),passes.push(pass),visitors.push(plugin.visitor)}for(const[plugin,pass]of passPairs)if(plugin.pre){const fn=(0,_async.maybeAsync)(plugin.pre,"You appear to be using an async plugin/preset, but Babel has been called synchronously");yield*fn.call(pass,file)}const visitor=_traverse().default.visitors.merge(visitors,passes,file.opts.wrapPluginVisitorMethod);(0,_traverse().default)(file.ast,visitor,file.scope);for(const[plugin,pass]of passPairs)if(plugin.post){const fn=(0,_async.maybeAsync)(plugin.post,"You appear to be using an async plugin/preset, but Babel has been called synchronously");yield*fn.call(pass,file)}}}(file,config.passes)}catch(e){var _opts$filename;throw e.message=`${null!=(_opts$filename=opts.filename)?_opts$filename:"unknown file"}: ${e.message}`,e.code||(e.code="BABEL_TRANSFORM_ERROR"),e}let outputCode,outputMap;try{!1!==opts.code&&({outputCode,outputMap}=(0,_generate.default)(config.passes,file))}catch(e){var _opts$filename2;throw e.message=`${null!=(_opts$filename2=opts.filename)?_opts$filename2:"unknown file"}: ${e.message}`,e.code||(e.code="BABEL_GENERATE_ERROR"),e}return{metadata:file.metadata,options:opts,ast:!0===opts.ast?file.ast:null,code:void 0===outputCode?null:outputCode,map:void 0===outputMap?null:outputMap,sourceType:file.ast.program.sourceType,externalDependencies:(0,_deepArray.flattenToSet)(config.externalDependencies)}};var _pluginPass=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/plugin-pass.js"),_blockHoistPlugin=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/block-hoist-plugin.js"),_normalizeOpts=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/normalize-opts.js"),_normalizeFile=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/normalize-file.js"),_generate=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/generate.js"),_deepArray=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/config/helpers/deep-array.js"),_async=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/gensync-utils/async.js")},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/normalize-file.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _fs(){const data=__webpack_require__("fs");return _fs=function(){return data},data}function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}function _debug(){const data=__webpack_require__("./node_modules/.pnpm/debug@4.4.0/node_modules/debug/src/index.js");return _debug=function(){return data},data}function _t(){const data=__webpack_require__("./node_modules/.pnpm/@babel+types@7.26.3/node_modules/@babel/types/lib/index.js");return _t=function(){return data},data}function _convertSourceMap(){const data=__webpack_require__("./node_modules/.pnpm/convert-source-map@2.0.0/node_modules/convert-source-map/index.js");return _convertSourceMap=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function*(pluginPasses,options,code,ast){if(code=`${code||""}`,ast){if("Program"===ast.type)ast=file(ast,[],[]);else if("File"!==ast.type)throw new Error("AST root must be a Program or File node");options.cloneInputAst&&(ast=(0,_cloneDeep.default)(ast))}else ast=yield*(0,_index.default)(pluginPasses,options,code);let inputMap=null;if(!1!==options.inputSourceMap){if("object"==typeof options.inputSourceMap&&(inputMap=_convertSourceMap().fromObject(options.inputSourceMap)),!inputMap){const lastComment=extractComments(INLINE_SOURCEMAP_REGEX,ast);if(lastComment)try{inputMap=_convertSourceMap().fromComment("//"+lastComment)}catch(err){debug("discarding unknown inline input sourcemap")}}if(!inputMap){const lastComment=extractComments(EXTERNAL_SOURCEMAP_REGEX,ast);if("string"==typeof options.filename&&lastComment)try{const match=EXTERNAL_SOURCEMAP_REGEX.exec(lastComment),inputMapContent=_fs().readFileSync(_path().resolve(_path().dirname(options.filename),match[1]),"utf8");inputMap=_convertSourceMap().fromJSON(inputMapContent)}catch(err){debug("discarding unknown file input sourcemap",err)}else lastComment&&debug("discarding un-loadable file input sourcemap")}}return new _file.default(options,{code,ast,inputMap})};var _file=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/file/file.js"),_index=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/parser/index.js"),_cloneDeep=__webpack_require__("./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/util/clone-deep.js");const{file,traverseFast}=_t(),debug=_debug()("babel:transform:file"),INLINE_SOURCEMAP_REGEX=/^[@#]\s+sourceMappingURL=data:(?:application|text)\/json;(?:charset[:=]\S+?;)?base64,.*$/,EXTERNAL_SOURCEMAP_REGEX=/^[@#][ \t]+sourceMappingURL=([^\s'"`]+)[ \t]*$/;function extractCommentsFromList(regex,comments,lastComment){return comments&&(comments=comments.filter((({value})=>!regex.test(value)||(lastComment=value,!1)))),[comments,lastComment]}function extractComments(regex,ast){let lastComment=null;return traverseFast(ast,(node=>{[node.leadingComments,lastComment]=extractCommentsFromList(regex,node.leadingComments,lastComment),[node.innerComments,lastComment]=extractCommentsFromList(regex,node.innerComments,lastComment),[node.trailingComments,lastComment]=extractCommentsFromList(regex,node.trailingComments,lastComment)})),lastComment}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/normalize-opts.js":(__unused_webpack_module,exports,__webpack_require__)=>{"use strict";function _path(){const data=__webpack_require__("path");return _path=function(){return data},data}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(config){const{filename,cwd,filenameRelative="string"==typeof filename?_path().relative(cwd,filename):"unknown",sourceType="module",inputSourceMap,sourceMaps=!!inputSourceMap,sourceRoot=config.options.moduleRoot,sourceFileName=_path().basename(filenameRelative),comments=!0,compact="auto"}=config.options,opts=config.options,options=Object.assign({},opts,{parserOpts:Object.assign({sourceType:".mjs"===_path().extname(filenameRelative)?"module":sourceType,sourceFileName:filename,plugins:[]},opts.parserOpts),generatorOpts:Object.assign({filename,auxiliaryCommentBefore:opts.auxiliaryCommentBefore,auxiliaryCommentAfter:opts.auxiliaryCommentAfter,retainLines:opts.retainLines,comments,shouldPrintComment:opts.shouldPrintComment,compact,minified:opts.minified,sourceMaps,sourceRoot,sourceFileName},opts.generatorOpts)});for(const plugins of config.passes)for(const plugin of plugins)plugin.manipulateOptions&&plugin.manipulateOptions(options,options.parserOpts);return options}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/plugin-pass.js":(__unused_webpack_module,exports)=>{"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;class PluginPass{constructor(file,key,options,isAsync){this._map=new Map,this.key=void 0,this.file=void 0,this.opts=void 0,this.cwd=void 0,this.filename=void 0,this.isAsync=void 0,this.key=key,this.file=file,this.opts=options||{},this.cwd=file.opts.cwd,this.filename=file.opts.filename,this.isAsync=isAsync}set(key,val){this._map.set(key,val)}get(key){return this._map.get(key)}availableHelper(name,versionRange){return this.file.availableHelper(name,versionRange)}addHelper(name){return this.file.addHelper(name)}buildCodeFrameError(node,msg,_Error){return this.file.buildCodeFrameError(node,msg,_Error)}}exports.default=PluginPass,PluginPass.prototype.getModuleName=function(){return this.file.getModuleName()},PluginPass.prototype.addImport=function(){this.file.addImport()}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/transformation/util/clone-deep.js":(__unused_webpack_module,exports)=>{"use strict";function deepClone(value,cache){if(null!==value){if(cache.has(value))return cache.get(value);let cloned;if(Array.isArray(value)){cloned=new Array(value.length),cache.set(value,cloned);for(let i=0;i<value.length;i++)cloned[i]="object"!=typeof value[i]?value[i]:deepClone(value[i],cache)}else{cloned={},cache.set(value,cloned);const keys=Object.keys(value);for(let i=0;i<keys.length;i++){const key=keys[i];cloned[key]="object"!=typeof value[key]?value[key]:deepClone(value[key],cache)}}return cloned}return value}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=function(value){return"object"!=typeof value?value:deepClone(value,new Map)}},"./node_modules/.pnpm/@babel+core@7.26.0/node_modules/@babel/core/lib/vendor/import-meta-resolve.js":(__unused_webpack_module,expor