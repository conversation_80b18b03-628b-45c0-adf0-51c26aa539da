{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'TZS'): string {\n  const formatter = new Intl.NumberFormat('en-TZ', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  })\n  \n  return formatter.format(amount)\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  return new Intl.DateTimeFormat('en-TZ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(dateObj)\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return 'just now'\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60)\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600)\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400)\n    return `${days} day${days > 1 ? 's' : ''} ago`\n  } else {\n    return formatDate(dateObj)\n  }\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  // Tanzania phone number validation (supports +255, 0, or direct format)\n  const phoneRegex = /^(\\+255|0)?[67]\\d{8}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAmB;IAChE,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,OAAO,UAAU,MAAM,CAAC;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;IACpD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;IAC9C,OAAO,IAAI,gBAAgB,QAAQ;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;IAC3C,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,wEAAwE;IACxE,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  isLoading?: boolean\n  asChild?: boolean\n  children: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading = false, asChild = false, children, disabled, ...props }, ref) => {\n    const baseStyles = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50'\n    \n    const variants = {\n      primary: 'bg-primary-500 text-white hover:bg-primary-600 focus-visible:ring-primary-500 active:bg-primary-700',\n      secondary: 'bg-secondary-500 text-secondary-foreground hover:bg-secondary-600 focus-visible:ring-secondary-500 active:bg-secondary-700',\n      accent: 'bg-accent-500 text-white hover:bg-accent-600 focus-visible:ring-accent-500 active:bg-accent-700',\n      outline: 'border border-neutral-300 bg-transparent text-neutral-700 hover:bg-neutral-50 focus-visible:ring-neutral-500 active:bg-neutral-100 dark:border-neutral-600 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:active:bg-neutral-700',\n      ghost: 'bg-transparent text-neutral-700 hover:bg-neutral-100 focus-visible:ring-neutral-500 active:bg-neutral-200 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:active:bg-neutral-700',\n      destructive: 'bg-red-500 text-white hover:bg-red-600 focus-visible:ring-red-500 active:bg-red-700'\n    }\n    \n    const sizes = {\n      sm: 'h-8 px-3 text-sm',\n      md: 'h-10 px-4 text-sm',\n      lg: 'h-12 px-6 text-base',\n      xl: 'h-14 px-8 text-lg'\n    }\n\n    const buttonClasses = cn(\n      baseStyles,\n      variants[variant],\n      sizes[size],\n      isLoading && 'cursor-not-allowed',\n      className\n    )\n\n    const content = isLoading ? (\n      <>\n        <svg\n          className=\"mr-2 h-4 w-4 animate-spin\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n        Loading...\n      </>\n    ) : (\n      children\n    )\n\n    if (asChild) {\n      return React.cloneElement(children as React.ReactElement, {\n        className: buttonClasses,\n        ...props\n      })\n    }\n\n    return (\n      <button\n        className={buttonClasses}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {content}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAoH;QAAnH,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,YAAY,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChH,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,sBACb;IAGF,MAAM,UAAU,0BACd;;0BACE,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAEA;;uBAIR;IAGF,IAAI,SAAS;QACX,qBAAO,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAgC;YACxD,WAAW;YACX,GAAG,KAAK;QACV;IACF;IAEA,qBACE,6LAAC;QACC,WAAW;QACX,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { cn } from '@/lib/utils'\n\ninterface HeaderProps {\n  user?: {\n    id: number\n    firstName: string\n    lastName: string\n    avatar?: string\n  } | null\n}\n\nexport function Header({ user }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  const navigation = [\n    { name: 'Feed', href: '/' },\n    { name: 'Providers', href: '/providers' },\n    { name: 'Clubs', href: '/clubs' },\n    { name: 'Marketplace', href: '/marketplace' },\n    { name: 'Jobs', href: '/jobs' },\n  ]\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-neutral-200 bg-white/80 backdrop-blur-md dark:border-neutral-800 dark:bg-neutral-900/80\">\n      <div className=\"container-mobile\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-primary-500 text-white font-bold\">\n              G\n            </div>\n            <span className=\"hidden font-display text-xl font-bold text-neutral-900 dark:text-neutral-100 sm:block\">\n              Glbiashara\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex md:items-center md:space-x-6\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-medium text-neutral-600 transition-colors hover:text-primary-600 dark:text-neutral-400 dark:hover:text-primary-400\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-3\">\n            {user ? (\n              <div className=\"flex items-center space-x-3\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"hidden sm:flex\">\n                  Create Post\n                </Button>\n                <div className=\"flex items-center space-x-2\">\n                  {user.avatar ? (\n                    <img\n                      src={user.avatar}\n                      alt={`${user.firstName} ${user.lastName}`}\n                      className=\"h-8 w-8 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary-500 text-sm font-medium text-white\">\n                      {user.firstName[0]}{user.lastName[0]}\n                    </div>\n                  )}\n                  <span className=\"hidden text-sm font-medium text-neutral-700 dark:text-neutral-300 sm:block\">\n                    {user.firstName}\n                  </span>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" size=\"sm\" asChild>\n                  <Link href=\"/auth/login\">Login</Link>\n                </Button>\n                <Button size=\"sm\" asChild>\n                  <Link href=\"/auth/register\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"flex h-8 w-8 items-center justify-center rounded-lg text-neutral-600 hover:bg-neutral-100 dark:text-neutral-400 dark:hover:bg-neutral-800 md:hidden\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              <svg\n                className=\"h-5 w-5\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                {isMobileMenuOpen ? (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                ) : (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"border-t border-neutral-200 py-4 dark:border-neutral-800 md:hidden\">\n            <nav className=\"flex flex-col space-y-3\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-sm font-medium text-neutral-600 transition-colors hover:text-primary-600 dark:text-neutral-400 dark:hover:text-primary-400\"\n                  onClick={() => setIsMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              {user && (\n                <Button variant=\"outline\" size=\"sm\" className=\"mt-2 w-full\">\n                  Create Post\n                </Button>\n              )}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAgBO,SAAS,OAAO,KAAqB;QAArB,EAAE,IAAI,EAAe,GAArB;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAe,MAAM;QAAe;QAC5C;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CAA0F;;;;;;8CAGzG,6LAAC;oCAAK,WAAU;8CAAwF;;;;;;;;;;;;sCAM1G,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,6LAAC;4BAAI,WAAU;;gCACZ,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAAiB;;;;;;sDAG7D,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,MAAM,iBACV,6LAAC;oDACC,KAAK,KAAK,MAAM;oDAChB,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;oDACvC,WAAU;;;;;yEAGZ,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,SAAS,CAAC,EAAE;wDAAE,KAAK,QAAQ,CAAC,EAAE;;;;;;;8DAGxC,6LAAC;oDAAK,WAAU;8DACb,KAAK,SAAS;;;;;;;;;;;;;;;;;yDAKrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO;sDACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAc;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAiB;;;;;;;;;;;;;;;;;8CAMlC,6LAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAEP,iCACC,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;iEAGJ,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASb,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;4BAQjB,sBACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E;GAlIgB;KAAA", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/layout/BottomNav.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\n\ninterface BottomNavProps {\n  user?: {\n    id: number\n    firstName: string\n    lastName: string\n    avatar?: string\n  } | null\n}\n\nexport function BottomNav({ user }: BottomNavProps) {\n  const pathname = usePathname()\n\n  const navigation = [\n    {\n      name: 'Feed',\n      href: '/',\n      icon: (\n        <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Explore',\n      href: '/explore',\n      icon: (\n        <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Create',\n      href: '/create',\n      icon: (\n        <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4v16m8-8H4\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Market',\n      href: '/marketplace',\n      icon: (\n        <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n        </svg>\n      )\n    },\n    {\n      name: 'Profile',\n      href: user ? '/profile' : '/auth/login',\n      icon: user ? (\n        user.avatar ? (\n          <img\n            src={user.avatar}\n            alt={`${user.firstName} ${user.lastName}`}\n            className=\"h-5 w-5 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"flex h-5 w-5 items-center justify-center rounded-full bg-primary-500 text-xs font-medium text-white\">\n            {user.firstName[0]}\n          </div>\n        )\n      ) : (\n        <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      )\n    }\n  ]\n\n  return (\n    <nav className=\"fixed bottom-0 left-0 right-0 z-50 border-t border-neutral-200 bg-white/80 backdrop-blur-md dark:border-neutral-800 dark:bg-neutral-900/80 md:hidden\">\n      <div className=\"grid h-16 grid-cols-5\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          \n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex flex-col items-center justify-center space-y-1 text-xs font-medium transition-colors',\n                isActive\n                  ? 'text-primary-600 dark:text-primary-400'\n                  : 'text-neutral-600 hover:text-primary-600 dark:text-neutral-400 dark:hover:text-primary-400'\n              )}\n            >\n              <div className={cn(\n                'transition-colors',\n                isActive\n                  ? 'text-primary-600 dark:text-primary-400'\n                  : 'text-neutral-600 dark:text-neutral-400'\n              )}>\n                {item.icon}\n              </div>\n              <span className=\"text-xs\">{item.name}</span>\n            </Link>\n          )\n        })}\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAgBO,SAAS,UAAU,KAAwB;QAAxB,EAAE,IAAI,EAAkB,GAAxB;;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM,OAAO,aAAa;YAC1B,MAAM,OACJ,KAAK,MAAM,iBACT,6LAAC;gBACC,KAAK,KAAK,MAAM;gBAChB,KAAK,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ;gBACvC,WAAU;;;;;qCAGZ,6LAAC;gBAAI,WAAU;0BACZ,KAAK,SAAS,CAAC,EAAE;;;;;qCAItB,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,WAAW,GAAG,CAAC,CAAC;gBACf,MAAM,WAAW,aAAa,KAAK,IAAI;gBAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oBAEH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA,WACI,2CACA;;sCAGN,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qBACA,WACI,2CACA;sCAEH,KAAK,IAAI;;;;;;sCAEZ,6LAAC;4BAAK,WAAU;sCAAW,KAAK,IAAI;;;;;;;mBAjB/B,KAAK,IAAI;;;;;YAoBpB;;;;;;;;;;;AAIR;GAhGgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}]}