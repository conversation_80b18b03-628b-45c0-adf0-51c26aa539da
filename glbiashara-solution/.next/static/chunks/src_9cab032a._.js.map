{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n  variant?: 'default' | 'elevated' | 'outlined'\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', padding = 'md', children, ...props }, ref) => {\n    const variants = {\n      default: 'bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700',\n      elevated: 'bg-white dark:bg-neutral-800 shadow-soft border border-neutral-200 dark:border-neutral-700',\n      outlined: 'bg-transparent border-2 border-neutral-300 dark:border-neutral-600'\n    }\n\n    const paddings = {\n      none: '',\n      sm: 'p-3',\n      md: 'p-4 sm:p-6',\n      lg: 'p-6 sm:p-8'\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-lg transition-colors',\n          variants[variant],\n          paddings[padding],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('mb-4 space-y-1.5', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardHeader.displayName = 'CardHeader'\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode\n  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'\n}\n\nconst CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(\n  ({ className, children, as: Component = 'h3', ...props }, ref) => {\n    return (\n      <Component\n        ref={ref}\n        className={cn(\n          'font-display text-lg font-semibold leading-none tracking-tight text-neutral-900 dark:text-neutral-100',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nCardTitle.displayName = 'CardTitle'\n\ninterface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-neutral-600 dark:text-neutral-400', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    )\n  }\n)\n\nCardDescription.displayName = 'CardDescription'\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardContent.displayName = 'CardContent'\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('mt-4 flex items-center space-x-2', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAQA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAyE;QAAxE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IACrE,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,WAAW;QACf,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG;AAOzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,QAA0D;QAAzD,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,YAAY,IAAI,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QACjB,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type = 'text', label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId()\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-neutral-400\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-12 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm transition-colors',\n              'placeholder:text-neutral-400',\n              'focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20',\n              'disabled:cursor-not-allowed disabled:opacity-50',\n              'dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-100 dark:placeholder:text-neutral-500',\n              'dark:focus:border-primary-400 dark:focus:ring-primary-400/20',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-neutral-500\">{helperText}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwF;QAAvF,EAAE,SAAS,EAAE,OAAO,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;;IACpF,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,KAAK;IAE3B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sGACA,gCACA,sFACA,mDACA,uGACA,gEACA,YAAY,SACZ,aAAa,SACb,SAAS,6DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/app/actions/providers.ts"], "sourcesContent": ["'use server'\n\nimport { prisma } from '@/lib/prisma'\nimport { getCurrentUser } from '@/lib/auth'\nimport { redirect } from 'next/navigation'\n\ninterface PurchaseServiceData {\n  providerId: number\n  serviceName: string\n  price: number\n  currency: string\n  phoneNumber: string\n  paymentMethod: string\n}\n\nexport async function purchaseService(data: PurchaseServiceData) {\n  try {\n    // Get current user\n    const currentUser = await getCurrentUser()\n    if (!currentUser) {\n      return { error: 'You must be logged in to purchase services' }\n    }\n\n    // Get user from database\n    const user = await prisma.user.findUnique({\n      where: { id: currentUser.userId }\n    })\n\n    if (!user) {\n      return { error: 'User not found' }\n    }\n\n    // Validate provider\n    const provider = await prisma.provider.findUnique({\n      where: { id: data.providerId }\n    })\n\n    if (!provider) {\n      return { error: 'Provider not found' }\n    }\n\n    // Create order\n    const order = await prisma.order.create({\n      data: {\n        buyerId: user.id,\n        sellerId: user.id, // For provider services, we can use the same user or create a system user\n        productId: 1, // We'll need to create a product entry for services\n        quantity: 1,\n        totalAmount: data.price,\n        currency: data.currency,\n        status: 'pending',\n        notes: `${data.serviceName} purchase from ${provider.name} for ${data.phoneNumber}`\n      }\n    })\n\n    // Create payment record\n    const payment = await prisma.payment.create({\n      data: {\n        userId: user.id,\n        orderId: order.id,\n        amount: data.price,\n        currency: data.currency,\n        method: data.paymentMethod,\n        status: 'pending',\n        metadata: {\n          providerId: data.providerId,\n          serviceName: data.serviceName,\n          phoneNumber: data.phoneNumber,\n          providerName: provider.name\n        }\n      }\n    })\n\n    // In a real implementation, here you would:\n    // 1. Integrate with payment gateway (M-Pesa, Stripe, etc.)\n    // 2. Process the payment\n    // 3. Update payment status\n    // 4. Notify the provider's API\n    // 5. Send confirmation to user\n\n    // For now, we'll simulate a successful purchase\n    await prisma.payment.update({\n      where: { id: payment.id },\n      data: { status: 'completed' }\n    })\n\n    await prisma.order.update({\n      where: { id: order.id },\n      data: { status: 'confirmed' }\n    })\n\n    return { \n      success: true, \n      orderId: order.id,\n      paymentId: payment.id,\n      message: `Successfully purchased ${data.serviceName} from ${provider.name}` \n    }\n  } catch (error) {\n    console.error('Purchase service error:', error)\n    return { error: 'Failed to process purchase. Please try again.' }\n  }\n}\n\nexport async function getProviderServices(providerId: number) {\n  try {\n    const provider = await prisma.provider.findUnique({\n      where: { id: providerId },\n      select: {\n        id: true,\n        name: true,\n        services: true\n      }\n    })\n\n    if (!provider) {\n      return { error: 'Provider not found' }\n    }\n\n    return { success: true, data: provider }\n  } catch (error) {\n    console.error('Error fetching provider services:', error)\n    return { error: 'Failed to fetch provider services' }\n  }\n}\n\nexport async function getUserPurchaseHistory() {\n  try {\n    const currentUser = await getCurrentUser()\n    if (!currentUser) {\n      return { error: 'You must be logged in to view purchase history' }\n    }\n\n    const orders = await prisma.order.findMany({\n      where: { buyerId: currentUser.userId },\n      include: {\n        payment: true\n      },\n      orderBy: { createdAt: 'desc' },\n      take: 20\n    })\n\n    return { success: true, data: orders }\n  } catch (error) {\n    console.error('Error fetching purchase history:', error)\n    return { error: 'Failed to fetch purchase history' }\n  }\n}\n\nexport async function createProviderFeedPost(data: {\n  providerId: number\n  title: string\n  description: string\n  mediaUrls?: string[]\n  tags?: string[]\n  isPromoted?: boolean\n}) {\n  try {\n    const currentUser = await getCurrentUser()\n    if (!currentUser) {\n      return { error: 'You must be logged in to create posts' }\n    }\n\n    // Verify provider exists\n    const provider = await prisma.provider.findUnique({\n      where: { id: data.providerId }\n    })\n\n    if (!provider) {\n      return { error: 'Provider not found' }\n    }\n\n    // Create feed item\n    const feedItem = await prisma.feedItem.create({\n      data: {\n        type: 'provider',\n        contentId: data.providerId,\n        userId: currentUser.userId,\n        title: data.title,\n        description: data.description,\n        mediaUrls: data.mediaUrls || [],\n        tags: data.tags || [],\n        isPromoted: data.isPromoted || false,\n        isActive: true\n      }\n    })\n\n    return { success: true, data: feedItem }\n  } catch (error) {\n    console.error('Error creating provider feed post:', error)\n    return { error: 'Failed to create feed post' }\n  }\n}\n\nexport async function searchProviders(query: string) {\n  try {\n    const providers = await prisma.provider.findMany({\n      where: {\n        AND: [\n          { isActive: true },\n          {\n            OR: [\n              { name: { contains: query, mode: 'insensitive' } },\n              { slug: { contains: query, mode: 'insensitive' } }\n            ]\n          }\n        ]\n      },\n      select: {\n        id: true,\n        name: true,\n        slug: true,\n        logo: true,\n        services: true\n      },\n      take: 10\n    })\n\n    return { success: true, data: providers }\n  } catch (error) {\n    console.error('Error searching providers:', error)\n    return { error: 'Failed to search providers' }\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAesB,kBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/providers/PurchaseModal.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { purchaseService } from '@/app/actions/providers'\nimport { formatCurrency, validatePhone } from '@/lib/utils'\n\ninterface PurchaseModalProps {\n  isOpen: boolean\n  onClose: () => void\n  service: {\n    name: string\n    price: number\n    currency: string\n    description: string\n    validity?: string\n  }\n  provider: {\n    id: number\n    name: string\n    logo?: string\n  }\n}\n\nexport function PurchaseModal({ isOpen, onClose, service, provider }: PurchaseModalProps) {\n  const [formData, setFormData] = useState({\n    phoneNumber: '',\n    paymentMethod: 'M-Pesa'\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [isLoading, setIsLoading] = useState(false)\n  const [step, setStep] = useState(1) // 1: Details, 2: Payment, 3: Confirmation\n\n  const paymentMethods = [\n    { id: 'M-Pesa', name: 'M-<PERSON><PERSON><PERSON>', icon: '💳' },\n    { id: 'Airtel Money', name: 'Airtel Money', icon: '💰' },\n    { id: 'Tigo Pesa', name: 'Tigo Pesa', icon: '💵' },\n    { id: 'Credit Card', name: 'Credit Card', icon: '💳' }\n  ]\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.phoneNumber) {\n      newErrors.phoneNumber = 'Phone number is required'\n    } else if (!validatePhone(formData.phoneNumber)) {\n      newErrors.phoneNumber = 'Please enter a valid phone number'\n    }\n\n    if (!formData.paymentMethod) {\n      newErrors.paymentMethod = 'Please select a payment method'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handlePurchase = async () => {\n    if (!validateForm()) return\n\n    setIsLoading(true)\n    \n    try {\n      const result = await purchaseService({\n        providerId: provider.id,\n        serviceName: service.name,\n        price: service.price,\n        currency: service.currency,\n        phoneNumber: formData.phoneNumber,\n        paymentMethod: formData.paymentMethod\n      })\n\n      if (result.success) {\n        setStep(3) // Show confirmation\n      } else {\n        setErrors({ general: result.error || 'Purchase failed. Please try again.' })\n      }\n    } catch (error) {\n      console.error('Purchase error:', error)\n      setErrors({ general: 'Purchase failed. Please try again.' })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    setStep(1)\n    setFormData({ phoneNumber: '', paymentMethod: 'M-Pesa' })\n    setErrors({})\n    onClose()\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              {provider.logo ? (\n                <img\n                  src={provider.logo}\n                  alt={`${provider.name} logo`}\n                  className=\"h-8 w-8 object-contain\"\n                />\n              ) : (\n                <div className=\"flex h-8 w-8 items-center justify-center rounded bg-primary-500 text-white font-bold text-sm\">\n                  {provider.name[0]}\n                </div>\n              )}\n              <div>\n                <CardTitle className=\"text-lg\">\n                  {step === 1 && 'Purchase Service'}\n                  {step === 2 && 'Payment Details'}\n                  {step === 3 && 'Purchase Successful'}\n                </CardTitle>\n                <CardDescription>{provider.name}</CardDescription>\n              </div>\n            </div>\n            <button\n              onClick={handleClose}\n              className=\"text-neutral-400 hover:text-neutral-600\"\n            >\n              <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </CardHeader>\n\n        <CardContent>\n          {step === 1 && (\n            <div className=\"space-y-4\">\n              {/* Service Details */}\n              <div className=\"rounded-lg bg-neutral-50 p-4 dark:bg-neutral-800\">\n                <h4 className=\"font-semibold text-neutral-900 dark:text-neutral-100\">\n                  {service.name}\n                </h4>\n                <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mt-1\">\n                  {service.description}\n                </p>\n                <div className=\"flex items-center justify-between mt-3\">\n                  <span className=\"text-lg font-bold text-primary-600 dark:text-primary-400\">\n                    {formatCurrency(service.price, service.currency)}\n                  </span>\n                  {service.validity && (\n                    <span className=\"text-sm text-neutral-500\">\n                      Valid for {service.validity}\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Phone Number Input */}\n              <Input\n                label=\"Phone Number\"\n                name=\"phoneNumber\"\n                value={formData.phoneNumber}\n                onChange={handleInputChange}\n                error={errors.phoneNumber}\n                placeholder=\"+255 123 456 789\"\n                helperText=\"Enter the phone number to receive the service\"\n                leftIcon={\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                }\n              />\n\n              {/* Payment Method Selection */}\n              <div>\n                <label className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\">\n                  Payment Method\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {paymentMethods.map((method) => (\n                    <button\n                      key={method.id}\n                      type=\"button\"\n                      onClick={() => setFormData(prev => ({ ...prev, paymentMethod: method.id }))}\n                      className={`flex items-center space-x-2 rounded-lg border p-3 text-left transition-colors ${\n                        formData.paymentMethod === method.id\n                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'\n                          : 'border-neutral-300 hover:bg-neutral-50 dark:border-neutral-600 dark:hover:bg-neutral-800'\n                      }`}\n                    >\n                      <span className=\"text-lg\">{method.icon}</span>\n                      <span className=\"text-sm font-medium\">{method.name}</span>\n                    </button>\n                  ))}\n                </div>\n                {errors.paymentMethod && (\n                  <p className=\"mt-1 text-sm text-red-500\">{errors.paymentMethod}</p>\n                )}\n              </div>\n\n              {errors.general && (\n                <div className=\"rounded-lg bg-red-50 p-3 text-sm text-red-600 dark:bg-red-900/20 dark:text-red-400\">\n                  {errors.general}\n                </div>\n              )}\n\n              <div className=\"flex space-x-3\">\n                <Button variant=\"outline\" onClick={handleClose} className=\"flex-1\">\n                  Cancel\n                </Button>\n                <Button onClick={() => setStep(2)} className=\"flex-1\">\n                  Continue\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {step === 2 && (\n            <div className=\"space-y-4\">\n              <div className=\"text-center\">\n                <h3 className=\"text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\n                  Confirm Purchase\n                </h3>\n                <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\n                  Please review your order details\n                </p>\n              </div>\n\n              <div className=\"rounded-lg border border-neutral-200 p-4 dark:border-neutral-700\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">Service:</span>\n                    <span className=\"text-sm font-medium\">{service.name}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">Phone:</span>\n                    <span className=\"text-sm font-medium\">{formData.phoneNumber}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">Payment:</span>\n                    <span className=\"text-sm font-medium\">{formData.paymentMethod}</span>\n                  </div>\n                  <div className=\"border-t border-neutral-200 pt-2 dark:border-neutral-700\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"font-semibold\">Total:</span>\n                      <span className=\"font-bold text-primary-600 dark:text-primary-400\">\n                        {formatCurrency(service.price, service.currency)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex space-x-3\">\n                <Button variant=\"outline\" onClick={() => setStep(1)} className=\"flex-1\">\n                  Back\n                </Button>\n                <Button \n                  onClick={handlePurchase} \n                  className=\"flex-1\"\n                  isLoading={isLoading}\n                  disabled={isLoading}\n                >\n                  Purchase Now\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {step === 3 && (\n            <div className=\"space-y-4 text-center\">\n              <div className=\"flex justify-center\">\n                <div className=\"flex h-16 w-16 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400\">\n                  <svg className=\"h-8 w-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                </div>\n              </div>\n              \n              <div>\n                <h3 className=\"text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\n                  Purchase Successful!\n                </h3>\n                <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mt-1\">\n                  Your {service.name} has been activated on {formData.phoneNumber}\n                </p>\n              </div>\n\n              <div className=\"rounded-lg bg-green-50 p-4 dark:bg-green-900/20\">\n                <p className=\"text-sm text-green-700 dark:text-green-400\">\n                  You will receive a confirmation SMS shortly. The service will be active within 5 minutes.\n                </p>\n              </div>\n\n              <Button onClick={handleClose} className=\"w-full\">\n                Done\n              </Button>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA0BO,SAAS,cAAc,KAA0D;QAA1D,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAsB,GAA1D;;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,aAAa;QACb,eAAe;IACjB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,0CAA0C;;IAE9E,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;QAC3C;YAAE,IAAI;YAAgB,MAAM;YAAgB,MAAM;QAAK;QACvD;YAAE,IAAI;YAAa,MAAM;YAAa,MAAM;QAAK;QACjD;YAAE,IAAI;YAAe,MAAM;YAAe,MAAM;QAAK;KACtD;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,UAAU,WAAW,GAAG;QAC1B,OAAO,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG;YAC/C,UAAU,WAAW,GAAG;QAC1B;QAEA,IAAI,CAAC,SAAS,aAAa,EAAE;YAC3B,UAAU,aAAa,GAAG;QAC5B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE;gBACnC,YAAY,SAAS,EAAE;gBACvB,aAAa,QAAQ,IAAI;gBACzB,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;gBAC1B,aAAa,SAAS,WAAW;gBACjC,eAAe,SAAS,aAAa;YACvC;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ,IAAG,oBAAoB;YACjC,OAAO;gBACL,UAAU;oBAAE,SAAS,OAAO,KAAK,IAAI;gBAAqC;YAC5E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,UAAU;gBAAE,SAAS;YAAqC;QAC5D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,YAAY;YAAE,aAAa;YAAI,eAAe;QAAS;QACvD,UAAU,CAAC;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,IAAI,iBACZ,6LAAC;wCACC,KAAK,SAAS,IAAI;wCAClB,KAAK,AAAC,GAAgB,OAAd,SAAS,IAAI,EAAC;wCACtB,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACZ,SAAS,IAAI,CAAC,EAAE;;;;;;kDAGrB,6LAAC;;0DACC,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;oDAClB,SAAS,KAAK;oDACd,SAAS,KAAK;oDACd,SAAS,KAAK;;;;;;;0DAEjB,6LAAC,mIAAA,CAAA,kBAAe;0DAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;0CAGnC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM7E,6LAAC,mIAAA,CAAA,cAAW;;wBACT,SAAS,mBACR,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,IAAI;;;;;;sDAEf,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,QAAQ;;;;;;gDAEhD,QAAQ,QAAQ,kBACf,6LAAC;oDAAK,WAAU;;wDAA2B;wDAC9B,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;8CAOnC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,OAAO,OAAO,WAAW;oCACzB,aAAY;oCACZ,YAAW;oCACX,wBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAM3E,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAwE;;;;;;sDAGzF,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oDAEC,MAAK;oDACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,eAAe,OAAO,EAAE;4DAAC,CAAC;oDACzE,WAAW,AAAC,iFAIX,OAHC,SAAS,aAAa,KAAK,OAAO,EAAE,GAChC,4DACA;;sEAGN,6LAAC;4DAAK,WAAU;sEAAW,OAAO,IAAI;;;;;;sEACtC,6LAAC;4DAAK,WAAU;sEAAuB,OAAO,IAAI;;;;;;;mDAV7C,OAAO,EAAE;;;;;;;;;;wCAcnB,OAAO,aAAa,kBACnB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,aAAa;;;;;;;;;;;;gCAIjE,OAAO,OAAO,kBACb,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO;;;;;;8CAInB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAa,WAAU;sDAAS;;;;;;sDAGnE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,QAAQ;4CAAI,WAAU;sDAAS;;;;;;;;;;;;;;;;;;wBAO3D,SAAS,mBACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+D;;;;;;sDAG7E,6LAAC;4CAAE,WAAU;sDAAiD;;;;;;;;;;;;8CAKhE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiD;;;;;;kEACjE,6LAAC;wDAAK,WAAU;kEAAuB,QAAQ,IAAI;;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiD;;;;;;kEACjE,6LAAC;wDAAK,WAAU;kEAAuB,SAAS,WAAW;;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiD;;;;;;kEACjE,6LAAC;wDAAK,WAAU;kEAAuB,SAAS,aAAa;;;;;;;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,QAAQ;4CAAI,WAAU;sDAAS;;;;;;sDAGxE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,WAAU;4CACV,WAAW;4CACX,UAAU;sDACX;;;;;;;;;;;;;;;;;;wBAON,SAAS,mBACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8CAK3E,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+D;;;;;;sDAG7E,6LAAC;4CAAE,WAAU;;gDAAsD;gDAC3D,QAAQ,IAAI;gDAAC;gDAAwB,SAAS,WAAW;;;;;;;;;;;;;8CAInE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;8CAK5D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/D;GA9RgB;KAAA", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/providers/ProviderPageClient.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { PurchaseModal } from './PurchaseModal'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface Service {\n  name: string\n  price: number\n  currency: string\n  description: string\n  validity?: string\n}\n\ninterface Provider {\n  id: number\n  name: string\n  logo?: string\n  services: Service[]\n}\n\ninterface ProviderPageClientProps {\n  provider: Provider\n}\n\nexport function ProviderPageClient({ provider }: ProviderPageClientProps) {\n  const [selectedService, setSelectedService] = useState<Service | null>(null)\n  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false)\n\n  const handlePurchaseClick = (service: Service) => {\n    setSelectedService(service)\n    setIsPurchaseModalOpen(true)\n  }\n\n  const handleClosePurchaseModal = () => {\n    setIsPurchaseModalOpen(false)\n    setSelectedService(null)\n  }\n\n  return (\n    <>\n      {/* Services Grid */}\n      {provider.services && provider.services.length > 0 ? (\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {provider.services.map((service, index) => (\n            <Card key={index} variant=\"elevated\" className=\"overflow-hidden transition-all hover:shadow-lg hover:-translate-y-1\">\n              <CardHeader className=\"pb-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <CardTitle className=\"text-lg font-bold\">{service.name}</CardTitle>\n                    <CardDescription className=\"mt-1\">{service.description}</CardDescription>\n                  </div>\n                  {(service as any).popular && (\n                    <div className=\"rounded-full bg-accent-100 px-3 py-1 text-xs font-medium text-accent-700 dark:bg-accent-900/20 dark:text-accent-400\">\n                      Popular\n                    </div>\n                  )}\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"pt-0 space-y-4\">\n                {/* Pricing */}\n                <div className=\"text-center py-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg\">\n                  <p className=\"text-3xl font-bold text-primary-600 dark:text-primary-400\">\n                    {formatCurrency(service.price, service.currency || 'TZS')}\n                  </p>\n                  {service.validity && (\n                    <p className=\"text-sm text-neutral-500 mt-1\">\n                      Valid for {service.validity}\n                    </p>\n                  )}\n                </div>\n\n                {/* Features */}\n                {(service as any).features && (service as any).features.length > 0 && (\n                  <div className=\"space-y-2\">\n                    <h4 className=\"text-sm font-semibold text-neutral-700 dark:text-neutral-300\">\n                      Features:\n                    </h4>\n                    <ul className=\"space-y-1\">\n                      {(service as any).features.map((feature: string, featureIndex: number) => (\n                        <li key={featureIndex} className=\"flex items-center space-x-2 text-sm text-neutral-600 dark:text-neutral-400\">\n                          <svg className=\"h-4 w-4 text-green-500 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                          </svg>\n                          <span>{feature}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                <Button\n                  className=\"w-full py-3 font-semibold\"\n                  onClick={() => handlePurchaseClick(service)}\n                >\n                  Purchase Now\n                </Button>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      ) : (\n        <Card className=\"text-center py-8\">\n          <CardContent>\n            <div className=\"flex flex-col items-center space-y-4\">\n              <svg className=\"h-12 w-12 text-neutral-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n              </svg>\n              <div>\n                <h3 className=\"text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\n                  No services available\n                </h3>\n                <p className=\"text-neutral-600 dark:text-neutral-400\">\n                  Check back later for new service offerings.\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Purchase Modal */}\n      {selectedService && (\n        <PurchaseModal\n          isOpen={isPurchaseModalOpen}\n          onClose={handleClosePurchaseModal}\n          service={selectedService}\n          provider={{\n            id: provider.id,\n            name: provider.name,\n            logo: provider.logo\n          }}\n        />\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA2BO,SAAS,mBAAmB,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,uBAAuB;IACzB;IAEA,MAAM,2BAA2B;QAC/B,uBAAuB;QACvB,mBAAmB;IACrB;IAEA,qBACE;;YAEG,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,kBAC/C,6LAAC;gBAAI,WAAU;0BACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC,mIAAA,CAAA,OAAI;wBAAa,SAAQ;wBAAW,WAAU;;0CAC7C,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAqB,QAAQ,IAAI;;;;;;8DACtD,6LAAC,mIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAQ,QAAQ,WAAW;;;;;;;;;;;;wCAEtD,QAAgB,OAAO,kBACvB,6LAAC;4CAAI,WAAU;sDAAsH;;;;;;;;;;;;;;;;;0CAO3I,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK,EAAE,QAAQ,QAAQ,IAAI;;;;;;4CAEpD,QAAQ,QAAQ,kBACf,6LAAC;gDAAE,WAAU;;oDAAgC;oDAChC,QAAQ,QAAQ;;;;;;;;;;;;;oCAM/B,QAAgB,QAAQ,IAAI,AAAC,QAAgB,QAAQ,CAAC,MAAM,GAAG,mBAC/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA+D;;;;;;0DAG7E,6LAAC;gDAAG,WAAU;0DACX,AAAC,QAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAiB,6BAC/C,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAI,WAAU;gEAAuC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC9F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;0EAEvE,6LAAC;0EAAM;;;;;;;uDAJA;;;;;;;;;;;;;;;;kDAWjB,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDACpC;;;;;;;;;;;;;uBAlDM;;;;;;;;;qCA0Df,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;gCAA6B,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACpF,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,6LAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU/D,iCACC,6LAAC,mJAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,UAAU;oBACR,IAAI,SAAS,EAAE;oBACf,MAAM,SAAS,IAAI;oBACnB,MAAM,SAAS,IAAI;gBACrB;;;;;;;;AAKV;GAhHgB;KAAA", "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/providers/ProviderBanners.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\n\ninterface Banner {\n  type: 'video' | 'image'\n  url: string\n  title: string\n  description: string\n}\n\ninterface ProviderBannersProps {\n  banners: Banner[]\n}\n\nexport function ProviderBanners({ banners }: ProviderBannersProps) {\n  const [currentBanner, setCurrentBanner] = useState(0)\n\n  if (!banners || banners.length === 0) {\n    return null\n  }\n\n  const nextBanner = () => {\n    setCurrentBanner((prev) => (prev + 1) % banners.length)\n  }\n\n  const prevBanner = () => {\n    setCurrentBanner((prev) => (prev - 1 + banners.length) % banners.length)\n  }\n\n  return (\n    <div className=\"bg-gradient-to-r from-primary-500 to-accent-500 py-8 md:py-12\">\n      <div className=\"container-mobile\">\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-2xl font-bold text-white md:text-3xl\">\n            Latest Offers & Promotions\n          </h2>\n          <p className=\"mt-2 text-primary-100\">\n            Don't miss out on our exclusive deals\n          </p>\n        </div>\n\n        <div className=\"relative\">\n          {/* Main Banner Display */}\n          <div className=\"relative overflow-hidden rounded-2xl bg-white shadow-2xl\">\n            {banners[currentBanner].type === 'video' ? (\n              <div className=\"aspect-video\">\n                <video\n                  className=\"h-full w-full object-cover\"\n                  controls\n                  poster=\"/providers/video-poster.jpg\"\n                >\n                  <source src={banners[currentBanner].url} type=\"video/mp4\" />\n                  Your browser does not support the video tag.\n                </video>\n              </div>\n            ) : (\n              <div className=\"aspect-video relative\">\n                <img\n                  src={banners[currentBanner].url}\n                  alt={banners[currentBanner].title}\n                  className=\"h-full w-full object-cover\"\n                />\n                {/* Overlay for image banners */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\" />\n                <div className=\"absolute bottom-0 left-0 right-0 p-6 text-white\">\n                  <h3 className=\"text-xl font-bold md:text-2xl\">\n                    {banners[currentBanner].title}\n                  </h3>\n                  <p className=\"mt-2 text-sm opacity-90 md:text-base\">\n                    {banners[currentBanner].description}\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* Navigation Arrows */}\n            {banners.length > 1 && (\n              <>\n                <button\n                  onClick={prevBanner}\n                  className=\"absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-white/20 p-2 text-white backdrop-blur-sm transition-all hover:bg-white/30\"\n                  aria-label=\"Previous banner\"\n                >\n                  <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                  </svg>\n                </button>\n                <button\n                  onClick={nextBanner}\n                  className=\"absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-white/20 p-2 text-white backdrop-blur-sm transition-all hover:bg-white/30\"\n                  aria-label=\"Next banner\"\n                >\n                  <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </button>\n              </>\n            )}\n          </div>\n\n          {/* Banner Info for Videos */}\n          {banners[currentBanner].type === 'video' && (\n            <div className=\"mt-4 rounded-lg bg-white p-4 dark:bg-neutral-800\">\n              <h3 className=\"text-lg font-bold text-neutral-900 dark:text-neutral-100\">\n                {banners[currentBanner].title}\n              </h3>\n              <p className=\"mt-1 text-neutral-600 dark:text-neutral-400\">\n                {banners[currentBanner].description}\n              </p>\n            </div>\n          )}\n\n          {/* Banner Indicators */}\n          {banners.length > 1 && (\n            <div className=\"mt-6 flex justify-center space-x-2\">\n              {banners.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentBanner(index)}\n                  className={`h-2 w-8 rounded-full transition-all ${\n                    index === currentBanner\n                      ? 'bg-white'\n                      : 'bg-white/40 hover:bg-white/60'\n                  }`}\n                  aria-label={`Go to banner ${index + 1}`}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Thumbnail Navigation */}\n        {banners.length > 1 && (\n          <div className=\"mt-6 grid grid-cols-2 gap-4 md:grid-cols-3\">\n            {banners.map((banner, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentBanner(index)}\n                className={`group relative overflow-hidden rounded-lg transition-all ${\n                  index === currentBanner\n                    ? 'ring-2 ring-white ring-offset-2 ring-offset-primary-500'\n                    : 'hover:scale-105'\n                }`}\n              >\n                <div className=\"aspect-video\">\n                  {banner.type === 'video' ? (\n                    <div className=\"flex h-full items-center justify-center bg-neutral-800\">\n                      <svg className=\"h-8 w-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M8 5v14l11-7z\" />\n                      </svg>\n                    </div>\n                  ) : (\n                    <img\n                      src={banner.url}\n                      alt={banner.title}\n                      className=\"h-full w-full object-cover\"\n                    />\n                  )}\n                </div>\n                <div className=\"absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover:opacity-100\" />\n                <div className=\"absolute bottom-0 left-0 right-0 p-2\">\n                  <p className=\"text-xs font-medium text-white opacity-0 transition-opacity group-hover:opacity-100\">\n                    {banner.title}\n                  </p>\n                </div>\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBO,SAAS,gBAAgB,KAAiC;QAAjC,EAAE,OAAO,EAAwB,GAAjC;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,iBAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;IACxD;IAEA,MAAM,aAAa;QACjB,iBAAiB,CAAC,OAAS,CAAC,OAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;gCACZ,OAAO,CAAC,cAAc,CAAC,IAAI,KAAK,wBAC/B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,QAAQ;wCACR,QAAO;;0DAEP,6LAAC;gDAAO,KAAK,OAAO,CAAC,cAAc,CAAC,GAAG;gDAAE,MAAK;;;;;;4CAAc;;;;;;;;;;;yDAKhE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,OAAO,CAAC,cAAc,CAAC,GAAG;4CAC/B,KAAK,OAAO,CAAC,cAAc,CAAC,KAAK;4CACjC,WAAU;;;;;;sDAGZ,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,OAAO,CAAC,cAAc,CAAC,KAAK;;;;;;8DAE/B,6LAAC;oDAAE,WAAU;8DACV,OAAO,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;gCAO1C,QAAQ,MAAM,GAAG,mBAChB;;sDACE,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;wBAQ9E,OAAO,CAAC,cAAc,CAAC,IAAI,KAAK,yBAC/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,OAAO,CAAC,cAAc,CAAC,KAAK;;;;;;8CAE/B,6LAAC;oCAAE,WAAU;8CACV,OAAO,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;wBAMxC,QAAQ,MAAM,GAAG,mBAChB,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,GAAG,sBACf,6LAAC;oCAEC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,AAAC,uCAIX,OAHC,UAAU,gBACN,aACA;oCAEN,cAAY,AAAC,gBAAyB,OAAV,QAAQ;mCAP/B;;;;;;;;;;;;;;;;gBAed,QAAQ,MAAM,GAAG,mBAChB,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4BAEC,SAAS,IAAM,iBAAiB;4BAChC,WAAW,AAAC,4DAIX,OAHC,UAAU,gBACN,4DACA;;8CAGN,6LAAC;oCAAI,WAAU;8CACZ,OAAO,IAAI,KAAK,wBACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAe,SAAQ;sDAC9D,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;6DAIZ,6LAAC;wCACC,KAAK,OAAO,GAAG;wCACf,KAAK,OAAO,KAAK;wCACjB,WAAU;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,OAAO,KAAK;;;;;;;;;;;;2BA1BZ;;;;;;;;;;;;;;;;;;;;;AAoCrB;GA9JgB;KAAA", "debugId": null}}]}