{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n  variant?: 'default' | 'elevated' | 'outlined'\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', padding = 'md', children, ...props }, ref) => {\n    const variants = {\n      default: 'bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700',\n      elevated: 'bg-white dark:bg-neutral-800 shadow-soft border border-neutral-200 dark:border-neutral-700',\n      outlined: 'bg-transparent border-2 border-neutral-300 dark:border-neutral-600'\n    }\n\n    const paddings = {\n      none: '',\n      sm: 'p-3',\n      md: 'p-4 sm:p-6',\n      lg: 'p-6 sm:p-8'\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-lg transition-colors',\n          variants[variant],\n          paddings[padding],\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('mb-4 space-y-1.5', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardHeader.displayName = 'CardHeader'\n\ninterface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode\n  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'\n}\n\nconst CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(\n  ({ className, children, as: Component = 'h3', ...props }, ref) => {\n    return (\n      <Component\n        ref={ref}\n        className={cn(\n          'font-display text-lg font-semibold leading-none tracking-tight text-neutral-900 dark:text-neutral-100',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </Component>\n    )\n  }\n)\n\nCardTitle.displayName = 'CardTitle'\n\ninterface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-neutral-600 dark:text-neutral-400', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    )\n  }\n)\n\nCardDescription.displayName = 'CardDescription'\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardContent.displayName = 'CardContent'\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('mt-4 flex items-center space-x-2', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAQA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAAyE;QAAxE,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,UAAU,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;IACrE,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,WAAW;QACf,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG;AAOzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,QAA0D;QAAzD,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,YAAY,IAAI,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QACjB,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  helperText?: string\n  leftIcon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type = 'text', label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId()\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 -translate-y-1/2 text-neutral-400\">\n              {leftIcon}\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-12 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm transition-colors',\n              'placeholder:text-neutral-400',\n              'focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20',\n              'disabled:cursor-not-allowed disabled:opacity-50',\n              'dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-100 dark:placeholder:text-neutral-500',\n              'dark:focus:border-primary-400 dark:focus:ring-primary-400/20',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus:border-red-500 focus:ring-red-500/20',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-500\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-neutral-500\">{helperText}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,sBAAQ,GAAA,6JAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,QAAwF;QAAvF,EAAE,SAAS,EAAE,OAAO,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;;IACpF,MAAM,UAAU,6JAAA,CAAA,UAAK,CAAC,KAAK;IAE3B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sGACA,gCACA,sFACA,mDACA,uGACA,gEACA,YAAY,SACZ,aAAa,SACb,SAAS,6DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAIN,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { validateEmail, validatePhone } from '@/lib/utils'\n\nconst countries = [\n  { code: 'TZ', name: 'Tanzania' },\n  { code: 'K<PERSON>', name: 'Kenya' },\n  { code: 'UG', name: 'Uganda' },\n  { code: 'RW', name: 'Rwanda' },\n  { code: 'US', name: 'United States' },\n  { code: 'GB', name: 'United Kingdom' },\n]\n\nconst professions = [\n  'Software Engineer',\n  'Teacher',\n  'Doctor',\n  'Nurse',\n  'Farmer',\n  'Business Owner',\n  'Student',\n  'Artist',\n  'Driver',\n  'Other'\n]\n\nexport default function RegisterPage() {\n  const router = useRouter()\n  const [step, setStep] = useState(1)\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: '',\n    country: 'TZ',\n    profession: '',\n    skills: [] as string[],\n    agreeToTerms: false\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target\n    const checked = (e.target as HTMLInputElement).checked\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }))\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }))\n    }\n  }\n\n  const validateStep1 = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.firstName.trim()) {\n      newErrors.firstName = 'First name is required'\n    }\n\n    if (!formData.lastName.trim()) {\n      newErrors.lastName = 'Last name is required'\n    }\n\n    if (!formData.email) {\n      newErrors.email = 'Email is required'\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address'\n    }\n\n    if (formData.phone && !validatePhone(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number'\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required'\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters'\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const validateStep2 = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.country) {\n      newErrors.country = 'Please select your country'\n    }\n\n    if (!formData.profession) {\n      newErrors.profession = 'Please select your profession'\n    }\n\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the terms and conditions'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleNext = () => {\n    if (step === 1 && validateStep1()) {\n      setStep(2)\n    }\n  }\n\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1)\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!validateStep2()) return\n\n    setIsLoading(true)\n\n    try {\n      const result = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData)\n      })\n\n      const data = await result.json()\n\n      if (data.success) {\n        router.push('/')\n        router.refresh()\n      } else {\n        setErrors({ general: data.error || 'Registration failed. Please try again.' })\n      }\n    } catch (error) {\n      console.error('Registration error:', error)\n      setErrors({ general: 'Registration failed. Please try again.' })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"container-mobile flex min-h-screen items-center justify-center py-12\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary-500 text-white\">\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\" />\n            </svg>\n          </div>\n          <CardTitle>Create your account</CardTitle>\n          <CardDescription>\n            Join Glbiashara to connect and grow your business\n          </CardDescription>\n          \n          {/* Progress indicator */}\n          <div className=\"mt-4 flex items-center justify-center space-x-2\">\n            <div className={`h-2 w-8 rounded-full ${step >= 1 ? 'bg-primary-500' : 'bg-neutral-200'}`} />\n            <div className={`h-2 w-8 rounded-full ${step >= 2 ? 'bg-primary-500' : 'bg-neutral-200'}`} />\n          </div>\n        </CardHeader>\n        \n        <CardContent>\n          {errors.general && (\n            <div className=\"mb-4 rounded-lg bg-red-50 p-3 text-sm text-red-600 dark:bg-red-900/20 dark:text-red-400\">\n              {errors.general}\n            </div>\n          )}\n\n          {step === 1 && (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-3\">\n                <Input\n                  label=\"First Name\"\n                  name=\"firstName\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  error={errors.firstName}\n                  placeholder=\"John\"\n                />\n                <Input\n                  label=\"Last Name\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  error={errors.lastName}\n                  placeholder=\"Doe\"\n                />\n              </div>\n              \n              <Input\n                label=\"Email\"\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                error={errors.email}\n                placeholder=\"<EMAIL>\"\n                leftIcon={\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\" />\n                  </svg>\n                }\n              />\n              \n              <Input\n                label=\"Phone (Optional)\"\n                type=\"tel\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleInputChange}\n                error={errors.phone}\n                placeholder=\"+255 123 456 789\"\n                helperText=\"We'll use this for important notifications\"\n                leftIcon={\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                }\n              />\n              \n              <Input\n                label=\"Password\"\n                type=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                placeholder=\"Create a strong password\"\n                leftIcon={\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                  </svg>\n                }\n              />\n              \n              <Input\n                label=\"Confirm Password\"\n                type=\"password\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                error={errors.confirmPassword}\n                placeholder=\"Confirm your password\"\n                leftIcon={\n                  <svg className=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                  </svg>\n                }\n              />\n              \n              <Button onClick={handleNext} className=\"w-full\">\n                Continue\n              </Button>\n            </div>\n          )}\n\n          {step === 2 && (\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div>\n                <label className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\">\n                  Country\n                </label>\n                <select\n                  name=\"country\"\n                  value={formData.country}\n                  onChange={handleInputChange}\n                  className=\"flex h-12 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-100\"\n                >\n                  {countries.map(country => (\n                    <option key={country.code} value={country.code}>\n                      {country.name}\n                    </option>\n                  ))}\n                </select>\n                {errors.country && (\n                  <p className=\"mt-1 text-sm text-red-500\">{errors.country}</p>\n                )}\n              </div>\n              \n              <div>\n                <label className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\">\n                  Profession\n                </label>\n                <select\n                  name=\"profession\"\n                  value={formData.profession}\n                  onChange={handleInputChange}\n                  className=\"flex h-12 w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500/20 dark:border-neutral-600 dark:bg-neutral-800 dark:text-neutral-100\"\n                >\n                  <option value=\"\">Select your profession</option>\n                  {professions.map(profession => (\n                    <option key={profession} value={profession}>\n                      {profession}\n                    </option>\n                  ))}\n                </select>\n                {errors.profession && (\n                  <p className=\"mt-1 text-sm text-red-500\">{errors.profession}</p>\n                )}\n              </div>\n              \n              <div className=\"space-y-3\">\n                <label className=\"flex items-start space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    name=\"agreeToTerms\"\n                    checked={formData.agreeToTerms}\n                    onChange={handleInputChange}\n                    className=\"mt-1 rounded border-neutral-300 text-primary-600 focus:ring-primary-500\"\n                  />\n                  <span className=\"text-sm text-neutral-600 dark:text-neutral-400\">\n                    I agree to the{' '}\n                    <Link href=\"/terms\" className=\"text-primary-600 hover:text-primary-500\">\n                      Terms of Service\n                    </Link>{' '}\n                    and{' '}\n                    <Link href=\"/privacy\" className=\"text-primary-600 hover:text-primary-500\">\n                      Privacy Policy\n                    </Link>\n                  </span>\n                </label>\n                {errors.agreeToTerms && (\n                  <p className=\"text-sm text-red-500\">{errors.agreeToTerms}</p>\n                )}\n              </div>\n              \n              <div className=\"flex space-x-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={handleBack}\n                  className=\"flex-1\"\n                >\n                  Back\n                </Button>\n                <Button\n                  type=\"submit\"\n                  className=\"flex-1\"\n                  isLoading={isLoading}\n                  disabled={isLoading}\n                >\n                  Create Account\n                </Button>\n              </div>\n            </form>\n          )}\n          \n          <div className=\"mt-6 text-center\">\n            <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\n              Already have an account?{' '}\n              <Link\n                href=\"/auth/login\"\n                className=\"font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400\"\n              >\n                Sign in\n              </Link>\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;IAAW;IAC/B;QAAE,MAAM;QAAM,MAAM;IAAQ;IAC5B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAgB;IACpC;QAAE,MAAM;QAAM,MAAM;IAAiB;CACtC;AAED,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,QAAQ,EAAE;QACV,cAAc;IAChB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,AAAC,EAAE,MAAM,CAAsB,OAAO;QAEtD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,GAAG;YACzC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,KAAK,IAAI,CAAC,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,KAAK,GAAG;YACpD,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,UAAU,YAAY,GAAG;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS,KAAK,iBAAiB;YACjC,QAAQ;QACV;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS,GAAG;YACd,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,iBAAiB;QAEtB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,MAAM,sBAAsB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,OAAO,IAAI;YAE9B,IAAI,KAAK,OAAO,EAAE;gBAChB,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB,OAAO;gBACL,UAAU;oBAAE,SAAS,KAAK,KAAK,IAAI;gBAAyC;YAC9E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,UAAU;gBAAE,SAAS;YAAyC;QAChE,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,AAAC,wBAAuE,OAAhD,QAAQ,IAAI,mBAAmB;;;;;;8CACvE,6LAAC;oCAAI,WAAW,AAAC,wBAAuE,OAAhD,QAAQ,IAAI,mBAAmB;;;;;;;;;;;;;;;;;;8BAI3E,6LAAC,mIAAA,CAAA,cAAW;;wBACT,OAAO,OAAO,kBACb,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO;;;;;;wBAIlB,SAAS,mBACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,OAAO,OAAO,SAAS;4CACvB,aAAY;;;;;;sDAEd,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,OAAO,QAAQ;4CACtB,aAAY;;;;;;;;;;;;8CAIhB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,OAAO,OAAO,KAAK;oCACnB,aAAY;oCACZ,wBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAK3E,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,OAAO,OAAO,KAAK;oCACnB,aAAY;oCACZ,YAAW;oCACX,wBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAK3E,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,OAAO,OAAO,QAAQ;oCACtB,aAAY;oCACZ,wBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAK3E,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU;oCACV,OAAO,OAAO,eAAe;oCAC7B,aAAY;oCACZ,wBACE,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAK3E,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAY,WAAU;8CAAS;;;;;;;;;;;;wBAMnD,SAAS,mBACR,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAwE;;;;;;sDAGzF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,WAAU;sDAET,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;oDAA0B,OAAO,QAAQ,IAAI;8DAC3C,QAAQ,IAAI;mDADF,QAAQ,IAAI;;;;;;;;;;wCAK5B,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;8CAI5D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAwE;;;;;;sDAGzF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU;4CACV,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,YAAY,GAAG,CAAC,CAAA,2BACf,6LAAC;wDAAwB,OAAO;kEAC7B;uDADU;;;;;;;;;;;wCAKhB,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,UAAU;;;;;;;;;;;;8CAI/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,SAAS,SAAS,YAAY;oDAC9B,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;;wDAAiD;wDAChD;sEACf,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAA0C;;;;;;wDAEhE;wDAAI;wDACR;sEACJ,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAA0C;;;;;;;;;;;;;;;;;;wCAK7E,OAAO,YAAY,kBAClB,6LAAC;4CAAE,WAAU;sDAAwB,OAAO,YAAY;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,WAAW;4CACX,UAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAiD;oCACnC;kDACzB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAjWwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}