{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\nimport { cookies } from 'next/headers'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: number\n  email: string\n  iat?: number\n  exp?: number\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n\n// Generate JWT token\nexport function generateToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })\n}\n\n// Verify JWT token\nexport function verifyToken(token: string): JWTPayload | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload\n  } catch (error) {\n    return null\n  }\n}\n\n// Get current user from cookies\nexport async function getCurrentUser(): Promise<JWTPayload | null> {\n  try {\n    const cookieStore = await cookies()\n    const token = cookieStore.get('auth-token')?.value\n    \n    if (!token) {\n      return null\n    }\n\n    return verifyToken(token)\n  } catch (error) {\n    return null\n  }\n}\n\n// Set auth cookie\nexport async function setAuthCookie(token: string) {\n  const cookieStore = await cookies()\n  cookieStore.set('auth-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 * 7, // 7 days\n    path: '/'\n  })\n}\n\n// Clear auth cookie\nexport async function clearAuthCookie() {\n  const cookieStore = await cookies()\n  cookieStore.delete('auth-token')\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,OAAwC;IACpE,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAK;AACzD;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,eAAe;QAE7C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO,YAAY;IACrB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,cAAc,KAAa;IAC/C,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,cAAc,OAAO;QACnC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;QACvB,MAAM;IACR;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'TZS'): string {\n  const formatter = new Intl.NumberFormat('en-TZ', {\n    style: 'currency',\n    currency: currency,\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  })\n  \n  return formatter.format(amount)\n}\n\nexport function formatDate(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  return new Intl.DateTimeFormat('en-TZ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(dateObj)\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) {\n    return 'just now'\n  } else if (diffInSeconds < 3600) {\n    const minutes = Math.floor(diffInSeconds / 60)\n    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`\n  } else if (diffInSeconds < 86400) {\n    const hours = Math.floor(diffInSeconds / 3600)\n    return `${hours} hour${hours > 1 ? 's' : ''} ago`\n  } else if (diffInSeconds < 604800) {\n    const days = Math.floor(diffInSeconds / 86400)\n    return `${days} day${days > 1 ? 's' : ''} ago`\n  } else {\n    return formatDate(dateObj)\n  }\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  // Tanzania phone number validation (supports +255, 0, or direct format)\n  const phoneRegex = /^(\\+255|0)?[67]\\d{8}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;IAEA,OAAO,UAAU,MAAM,CAAC;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT,OAAO,IAAI,gBAAgB,MAAM;QAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;IACzD,OAAO,IAAI,gBAAgB,OAAO;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;QACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;IACnD,OAAO,IAAI,gBAAgB,QAAQ;QACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;QACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;IAChD,OAAO;QACL,OAAO,WAAW;IACpB;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,wEAAwE;IACxE,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/app/actions/auth.ts"], "sourcesContent": ["'use server'\n\nimport { redirect } from 'next/navigation'\nimport { prisma } from '@/lib/prisma'\nimport { hashPassword, verifyPassword, generateToken, setAuthCookie, clearAuthCookie } from '@/lib/auth'\nimport { validateEmail, validatePhone } from '@/lib/utils'\n\ninterface RegisterData {\n  firstName: string\n  lastName: string\n  email: string\n  phone?: string\n  password: string\n  country: string\n  profession?: string\n  skills?: string[]\n}\n\ninterface LoginData {\n  email: string\n  password: string\n}\n\nexport async function registerUser(data: RegisterData) {\n  try {\n    // Validate input\n    if (!data.firstName || !data.lastName || !data.email || !data.password) {\n      return { error: 'All required fields must be provided' }\n    }\n\n    if (!validateEmail(data.email)) {\n      return { error: 'Invalid email address' }\n    }\n\n    if (data.phone && !validatePhone(data.phone)) {\n      return { error: 'Invalid phone number' }\n    }\n\n    if (data.password.length < 6) {\n      return { error: 'Password must be at least 6 characters long' }\n    }\n\n    // Check if user already exists\n    const existingUser = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { email: data.email },\n          ...(data.phone ? [{ phone: data.phone }] : [])\n        ]\n      }\n    })\n\n    if (existingUser) {\n      return { error: 'User with this email or phone already exists' }\n    }\n\n    // Hash password\n    const hashedPassword = await hashPassword(data.password)\n\n    // Create user\n    const user = await prisma.user.create({\n      data: {\n        firstName: data.firstName,\n        lastName: data.lastName,\n        email: data.email,\n        phone: data.phone || null,\n        password: hashedPassword,\n        country: data.country,\n        profession: data.profession || null,\n        skills: data.skills || [],\n        isVerified: false\n      },\n      select: {\n        id: true,\n        firstName: true,\n        lastName: true,\n        email: true,\n        country: true,\n        profession: true\n      }\n    })\n\n    // Generate JWT token\n    const token = generateToken({\n      userId: user.id,\n      email: user.email\n    })\n\n    // Set auth cookie\n    await setAuthCookie(token)\n\n    return { success: true, user }\n  } catch (error) {\n    console.error('Registration error:', error)\n    return { error: 'Registration failed. Please try again.' }\n  }\n}\n\nexport async function loginUser(data: LoginData) {\n  try {\n    // Validate input\n    if (!data.email || !data.password) {\n      return { error: 'Email and password are required' }\n    }\n\n    if (!validateEmail(data.email)) {\n      return { error: 'Invalid email address' }\n    }\n\n    // Find user\n    const user = await prisma.user.findUnique({\n      where: { email: data.email },\n      select: {\n        id: true,\n        firstName: true,\n        lastName: true,\n        email: true,\n        password: true,\n        country: true,\n        profession: true,\n        avatar: true,\n        isVerified: true\n      }\n    })\n\n    if (!user) {\n      return { error: 'Invalid email or password' }\n    }\n\n    // Verify password\n    const isValidPassword = await verifyPassword(data.password, user.password)\n    if (!isValidPassword) {\n      return { error: 'Invalid email or password' }\n    }\n\n    // Generate JWT token\n    const token = generateToken({\n      userId: user.id,\n      email: user.email\n    })\n\n    // Set auth cookie\n    await setAuthCookie(token)\n\n    // Return user data (without password)\n    const { password, ...userWithoutPassword } = user\n    return { success: true, user: userWithoutPassword }\n  } catch (error) {\n    console.error('Login error:', error)\n    return { error: 'Login failed. Please try again.' }\n  }\n}\n\nexport async function logoutUser() {\n  try {\n    await clearAuthCookie()\n    return { success: true }\n  } catch (error) {\n    console.error('Logout error:', error)\n    return { error: 'Logout failed. Please try again.' }\n  }\n}\n\nexport async function getCurrentUserProfile() {\n  try {\n    // This would typically get the user from the JWT token\n    // For now, we'll implement a basic version\n    return { user: null }\n  } catch (error) {\n    console.error('Get current user error:', error)\n    return { user: null }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;;;;;;AAkBO,eAAe,aAAa,IAAkB;IACnD,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,QAAQ,EAAE;YACtE,OAAO;gBAAE,OAAO;YAAuC;QACzD;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,GAAG;YAC9B,OAAO;gBAAE,OAAO;YAAwB;QAC1C;QAEA,IAAI,KAAK,KAAK,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,GAAG;YAC5C,OAAO;gBAAE,OAAO;YAAuB;QACzC;QAEA,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC5B,OAAO;gBAAE,OAAO;YAA8C;QAChE;QAEA,+BAA+B;QAC/B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC/C,OAAO;gBACL,IAAI;oBACF;wBAAE,OAAO,KAAK,KAAK;oBAAC;uBAChB,KAAK,KAAK,GAAG;wBAAC;4BAAE,OAAO,KAAK,KAAK;wBAAC;qBAAE,GAAG,EAAE;iBAC9C;YACH;QACF;QAEA,IAAI,cAAc;YAChB,OAAO;gBAAE,OAAO;YAA+C;QACjE;QAEA,gBAAgB;QAChB,MAAM,iBAAiB,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ;QAEvD,cAAc;QACd,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU;gBACV,SAAS,KAAK,OAAO;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,QAAQ,KAAK,MAAM,IAAI,EAAE;gBACzB,YAAY;YACd;YACA,QAAQ;gBACN,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,YAAY;YACd;QACF;QAEA,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;YAC1B,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;QACnB;QAEA,kBAAkB;QAClB,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;QAEpB,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,OAAO;QAAyC;IAC3D;AACF;AAEO,eAAe,UAAU,IAAe;IAC7C,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,QAAQ,EAAE;YACjC,OAAO;gBAAE,OAAO;YAAkC;QACpD;QAEA,IAAI,CAAC,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,GAAG;YAC9B,OAAO;gBAAE,OAAO;YAAwB;QAC1C;QAEA,YAAY;QACZ,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,OAAO,KAAK,KAAK;YAAC;YAC3B,QAAQ;gBACN,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,YAAY;gBACZ,QAAQ;gBACR,YAAY;YACd;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,OAAO;YAA4B;QAC9C;QAEA,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ;QACzE,IAAI,CAAC,iBAAiB;YACpB,OAAO;gBAAE,OAAO;YAA4B;QAC9C;QAEA,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;YAC1B,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;QACnB;QAEA,kBAAkB;QAClB,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE;QAEpB,sCAAsC;QACtC,MAAM,EAAE,QAAQ,EAAE,GAAG,qBAAqB,GAAG;QAC7C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAoB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,OAAO;QAAkC;IACpD;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;QACpB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YAAE,OAAO;QAAmC;IACrD;AACF;AAEO,eAAe;IACpB,IAAI;QACF,uDAAuD;QACvD,2CAA2C;QAC3C,OAAO;YAAE,MAAM;QAAK;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,MAAM;QAAK;IACtB;AACF;;;IArJsB;IA2EA;IAuDA;IAUA;;AA5IA,iPAAA;AA2EA,iPAAA;AAuDA,iPAAA;AAUA,iPAAA", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/last/glbiashara-solution/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { registerUser } from '@/app/actions/auth'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const result = await registerUser(body)\n    \n    if (result.success) {\n      return NextResponse.json(result, { status: 201 })\n    } else {\n      return NextResponse.json(result, { status: 400 })\n    }\n  } catch (error) {\n    console.error('Registration API error:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;QAElC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;gBAAE,QAAQ;YAAI;QACjD,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;gBAAE,QAAQ;YAAI;QACjD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}