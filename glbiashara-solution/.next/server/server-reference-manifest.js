self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00018276428f728112cc8517b57138dc1a5adec910\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/providers/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/providers/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/providers/page\": \"rsc\"\n      }\n    },\n    \"00538fe1cd9b075f0c6a71ee725f369f10abbd6522\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/providers/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/providers/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/providers/page\": \"rsc\"\n      }\n    },\n    \"00af4b3772d1844a1cb7dc6bb3b30ce6ae835414fe\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/providers/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/providers/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/providers/page\": \"rsc\"\n      }\n    },\n    \"60f46a7ba1c3811e50f2e1aeb8d42e8472ddee3403\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/providers/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/providers/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/feed.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\",\n        \"app/providers/page\": \"rsc\"\n      }\n    },\n    \"4038b7915aaf0d0815357eed0ec0edfc9a77cca07a\": {\n      \"workers\": {\n        \"app/providers/[slug]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/providers/[slug]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/actions/providers.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/providers/[slug]/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"rLHWG3QbVDBgU2viHUaF2GevD/ltr5lSf0EqAQpbveY=\"\n}"