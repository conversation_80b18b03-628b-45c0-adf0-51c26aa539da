import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../src/lib/auth'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create providers
  const vodacom = await prisma.provider.create({
    data: {
      name: 'Vodacom Tanzania',
      slug: 'vodacom',
      logo: '/providers/vodacom-logo.png',
      content: {
        description: 'Leading telecommunications provider in Tanzania',
        website: 'https://vodacom.co.tz',
        services: ['Mobile', 'Internet', 'M-Pesa']
      },
      services: [
        {
          name: '10GB Data Bundle',
          price: 10000,
          currency: 'TZS',
          validity: '30 days',
          description: 'High-speed internet for a month'
        },
        {
          name: '5GB Data Bundle',
          price: 5000,
          currency: 'TZS',
          validity: '7 days',
          description: 'Perfect for weekly usage'
        }
      ],
      isActive: true
    }
  })

  const airtel = await prisma.provider.create({
    data: {
      name: 'Airtel Tanzania',
      slug: 'airtel',
      logo: '/providers/airtel-logo.png',
      content: {
        description: 'Affordable telecommunications solutions',
        website: 'https://airtel.co.tz',
        services: ['Mobile', 'Internet', 'Airtel Money']
      },
      services: [
        {
          name: '15GB Data Bundle',
          price: 12000,
          currency: 'TZS',
          validity: '30 days',
          description: 'Best value data package'
        }
      ],
      isActive: true
    }
  })

  // Create clubs
  const simba = await prisma.club.create({
    data: {
      name: 'Simba Sports Club',
      slug: 'simba-sc',
      sport: 'Football',
      logo: '/clubs/simba-logo.png',
      content: {
        description: 'The most successful football club in Tanzania',
        founded: '1936',
        stadium: 'Benjamin Mkapa Stadium',
        achievements: ['28 League Titles', '5 Cup Titles']
      },
      isActive: true
    }
  })

  const yanga = await prisma.club.create({
    data: {
      name: 'Young Africans Sports Club',
      slug: 'yanga-sc',
      sport: 'Football',
      logo: '/clubs/yanga-logo.png',
      content: {
        description: 'Historic football club with passionate fans',
        founded: '1935',
        stadium: 'Benjamin Mkapa Stadium',
        achievements: ['27 League Titles', '4 Cup Titles']
      },
      isActive: true
    }
  })

  // Create institutions
  const udsm = await prisma.institution.create({
    data: {
      name: 'University of Dar es Salaam',
      slug: 'udsm',
      level: 'University',
      logo: '/institutions/udsm-logo.png',
      content: {
        description: 'Premier university in Tanzania',
        established: '1961',
        faculties: ['Engineering', 'Medicine', 'Law', 'Business'],
        students: 40000
      },
      isActive: true
    }
  })

  // Create companies
  const techCompany = await prisma.company.create({
    data: {
      name: 'TechHub Tanzania',
      slug: 'techhub-tz',
      industry: 'Technology',
      skills: ['JavaScript', 'React', 'Node.js', 'Python', 'Mobile Development'],
      logo: '/companies/techhub-logo.png',
      content: {
        description: 'Leading software development company in East Africa',
        founded: '2015',
        employees: 150,
        services: ['Web Development', 'Mobile Apps', 'Cloud Solutions']
      },
      isActive: true
    }
  })

  // Create a test user
  const hashedPassword = await hashPassword('password123')
  const testUser = await prisma.user.create({
    data: {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      country: 'TZ',
      profession: 'Software Engineer',
      skills: ['JavaScript', 'React', 'Node.js'],
      isVerified: true
    }
  })

  // Create CV for test user
  await prisma.cV.create({
    data: {
      userId: testUser.id,
      personal: {
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '+255123456789',
        location: 'Dar es Salaam, Tanzania'
      },
      education: [
        {
          institution: 'University of Dar es Salaam',
          degree: 'Bachelor of Computer Science',
          year: '2020',
          grade: 'First Class'
        }
      ],
      skills: ['JavaScript', 'React', 'Node.js', 'PostgreSQL', 'Docker'],
      experience: [
        {
          company: 'TechHub Tanzania',
          position: 'Software Developer',
          duration: '2020 - Present',
          description: 'Full-stack web development using modern technologies'
        }
      ],
      summary: 'Passionate software engineer with 3+ years of experience in web development',
      isPublic: true
    }
  })

  // Create portfolio for test user
  const portfolio = await prisma.portfolio.create({
    data: {
      userId: testUser.id,
      title: 'E-commerce Web Application',
      description: 'Full-stack e-commerce platform built with React and Node.js, featuring payment integration and inventory management.',
      mediaUrls: ['/portfolio/ecommerce-1.jpg', '/portfolio/ecommerce-2.jpg'],
      skills: ['React', 'Node.js', 'PostgreSQL', 'Stripe API'],
      projectUrl: 'https://demo-ecommerce.com',
      githubUrl: 'https://github.com/testuser/ecommerce',
      isPublished: true
    }
  })

  // Create product for test user
  const product = await prisma.product.create({
    data: {
      userId: testUser.id,
      title: 'Custom Web Development Services',
      description: 'Professional web development services for small and medium businesses. Specializing in modern, responsive websites and web applications.',
      price: 500000,
      currency: 'TZS',
      mediaUrls: ['/products/web-dev-1.jpg', '/products/web-dev-2.jpg'],
      category: 'Services',
      location: 'Dar es Salaam, TZ',
      tags: ['Web Development', 'React', 'Node.js', 'Custom Software'],
      isActive: true
    }
  })

  // Create innovation
  const innovation = await prisma.innovation.create({
    data: {
      userId: testUser.id,
      institutionId: udsm.id,
      title: 'Smart Water Management System',
      description: 'IoT-based water management system for urban areas, featuring real-time monitoring and automated distribution control.',
      mediaUrls: ['/innovations/water-system-1.jpg', '/innovations/water-system-2.jpg'],
      tags: ['IoT', 'Water Management', 'Smart City', 'Sustainability'],
      isPublished: true
    }
  })

  // Create feed items
  await prisma.feedItem.create({
    data: {
      type: 'provider',
      contentId: vodacom.id,
      title: 'New 10GB Data Bundle - Only 10,000 TZS!',
      description: 'Get high-speed internet for a whole month. Perfect for streaming, browsing, and staying connected.',
      mediaUrls: ['/feed/vodacom-data-offer.jpg'],
      tags: ['Data Bundle', 'Internet', 'Promotion'],
      isPromoted: true,
      isActive: true
    }
  })

  await prisma.feedItem.create({
    data: {
      type: 'club',
      contentId: simba.id,
      title: 'Simba vs Yanga - The Derby of the Century!',
      description: 'Get ready for the biggest match of the season. Tickets available now at all outlets.',
      mediaUrls: ['/feed/simba-yanga-derby.jpg'],
      tags: ['Football', 'Derby', 'Simba', 'Yanga'],
      isPromoted: false,
      isActive: true
    }
  })

  await prisma.feedItem.create({
    data: {
      type: 'portfolio',
      contentId: portfolio.id,
      userId: testUser.id,
      title: 'E-commerce Web Application Portfolio',
      description: 'Check out this amazing e-commerce platform built with modern technologies.',
      mediaUrls: portfolio.mediaUrls,
      tags: ['Portfolio', 'Web Development', 'E-commerce'],
      isPromoted: false,
      isActive: true
    }
  })

  await prisma.feedItem.create({
    data: {
      type: 'product',
      contentId: product.id,
      userId: testUser.id,
      title: 'Professional Web Development Services',
      description: 'Transform your business with a modern, responsive website. Contact us for a free consultation.',
      mediaUrls: product.mediaUrls,
      tags: ['Web Development', 'Services', 'Business'],
      isPromoted: false,
      isActive: true
    }
  })

  await prisma.feedItem.create({
    data: {
      type: 'innovation',
      contentId: innovation.id,
      userId: testUser.id,
      title: 'UDSM Students Develop Smart Water Management System',
      description: 'Revolutionary IoT solution for urban water distribution and monitoring.',
      mediaUrls: innovation.mediaUrls,
      tags: ['Innovation', 'IoT', 'UDSM', 'Water Management'],
      isPromoted: false,
      isActive: true
    }
  })

  console.log('✅ Database seeding completed successfully!')
  console.log(`Created:
  - 2 Providers (Vodacom, Airtel)
  - 2 Clubs (Simba, Yanga)
  - 1 Institution (UDSM)
  - 1 Company (TechHub)
  - 1 Test User with CV, Portfolio, Product, and Innovation
  - 5 Feed Items`)
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
