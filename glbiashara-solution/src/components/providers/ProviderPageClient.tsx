'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { PurchaseModal } from './PurchaseModal'
import { formatCurrency } from '@/lib/utils'

interface Service {
  name: string
  price: number
  currency: string
  description: string
  validity?: string
}

interface Provider {
  id: number
  name: string
  logo?: string
  services: Service[]
}

interface ProviderPageClientProps {
  provider: Provider
}

export function ProviderPageClient({ provider }: ProviderPageClientProps) {
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [isPurchaseModalOpen, setIsPurchaseModalOpen] = useState(false)

  const handlePurchaseClick = (service: Service) => {
    setSelectedService(service)
    setIsPurchaseModalOpen(true)
  }

  const handleClosePurchaseModal = () => {
    setIsPurchaseModalOpen(false)
    setSelectedService(null)
  }

  return (
    <>
      {/* Services Grid */}
      {provider.services && provider.services.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {provider.services.map((service, index) => (
            <Card key={index} variant="elevated" className="overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{service.name}</CardTitle>
                <CardDescription>{service.description}</CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                      {formatCurrency(service.price, service.currency || 'TZS')}
                    </p>
                    {service.validity && (
                      <p className="text-sm text-neutral-500">
                        Valid for {service.validity}
                      </p>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="rounded-full bg-accent-100 px-3 py-1 text-xs font-medium text-accent-700 dark:bg-accent-900/20 dark:text-accent-400">
                      Popular
                    </div>
                  </div>
                </div>
                
                <Button 
                  className="w-full"
                  onClick={() => handlePurchaseClick(service)}
                >
                  Purchase Now
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="text-center py-8">
          <CardContent>
            <div className="flex flex-col items-center space-y-4">
              <svg className="h-12 w-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <div>
                <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
                  No services available
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400">
                  Check back later for new service offerings.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Purchase Modal */}
      {selectedService && (
        <PurchaseModal
          isOpen={isPurchaseModalOpen}
          onClose={handleClosePurchaseModal}
          service={selectedService}
          provider={{
            id: provider.id,
            name: provider.name,
            logo: provider.logo
          }}
        />
      )}
    </>
  )
}
