@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #0ea5e9;
  --primary-foreground: #ffffff;
  --secondary: #eab308;
  --secondary-foreground: #422006;
  --accent: #22c55e;
  --accent-foreground: #052e16;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --input: #ffffff;
  --ring: #0ea5e9;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #38bdf8;
    --primary-foreground: #082f49;
    --secondary: #fde047;
    --secondary-foreground: #422006;
    --accent: #4ade80;
    --accent-foreground: #052e16;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --border: #404040;
    --input: #262626;
    --ring: #38bdf8;
  }
}

/* Base Styles */
* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-first responsive typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

h1 {
  font-size: 1.875rem;
  /* 30px */
  font-weight: 700;
}

h2 {
  font-size: 1.5rem;
  /* 24px */
}

h3 {
  font-size: 1.25rem;
  /* 20px */
}

h4 {
  font-size: 1.125rem;
  /* 18px */
}

/* Tablet and up */
@media (min-width: 768px) {
  h1 {
    font-size: 2.25rem;
    /* 36px */
  }

  h2 {
    font-size: 1.875rem;
    /* 30px */
  }

  h3 {
    font-size: 1.5rem;
    /* 24px */
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  h1 {
    font-size: 3rem;
    /* 48px */
  }

  h2 {
    font-size: 2.25rem;
    /* 36px */
  }

  h3 {
    font-size: 1.875rem;
    /* 30px */
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Custom utility classes */
.text-balance {
  text-wrap: balance;
}

.container-mobile {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-mobile {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container-mobile {
    max-width: 768px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-mobile {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-mobile {
    max-width: 1280px;
  }
}